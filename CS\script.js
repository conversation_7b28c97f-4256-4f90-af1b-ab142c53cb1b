// Insurance In Bazaar - Coming Soon JavaScript

// Set launch date globally (7 days from now)
const LAUNCH_DATE = new Date();
LAUNCH_DATE.setDate(LAUNCH_DATE.getDate() + 7);

// Countdown Timer - Set to launch in 7 days
function updateCountdown() {
    // Get current time
    const now = new Date().getTime();
    const distance = LAUNCH_DATE.getTime() - now;

    // Debug logging (remove in production)
    if (Math.floor(now / 1000) % 10 === 0) { // Log every 10 seconds
        console.log('Countdown Debug:', {
            now: new Date(now),
            launchDate: LAUNCH_DATE,
            distance: distance,
            distanceInDays: distance / (1000 * 60 * 60 * 24)
        });
    }

    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    // Add animation effect when numbers change
    const elements = ['days', 'hours', 'minutes', 'seconds'];
    const values = [days, hours, minutes, seconds];

    elements.forEach((id, index) => {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`Countdown element with id '${id}' not found`);
            return;
        }

        const newValue = values[index].toString().padStart(2, '0');

        if (element.textContent !== newValue) {
            element.style.transform = 'scale(1.2)';
            element.style.color = '#ffd700';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.color = '#ffd700';
            }, 200);
        }

        element.textContent = newValue;
    });

    // Special effects when countdown gets low
    if (distance > 0 && distance < 24 * 60 * 60 * 1000) { // Less than 24 hours
        document.querySelector('.countdown').style.animation = 'countdownPulse 1s ease-in-out infinite';
        document.querySelector('.main-title').style.color = '#ff6b6b';
    } else if (distance > 0 && distance < 7 * 24 * 60 * 60 * 1000) { // Less than 7 days
        document.querySelector('.countdown').style.animation = 'countdownPulse 1.5s ease-in-out infinite';
    }

    if (distance < 0) {
        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.innerHTML =
                '<div style="font-size: 2rem; color: #ffd700; animation: logoGlow 1s ease-in-out infinite alternate; text-align: center;"><i class="fas fa-rocket"></i> We\'re Live! <i class="fas fa-party-horn"></i></div>';

            // Trigger celebration fireworks when countdown reaches zero
            if (typeof createCelebrationFireworks === 'function') {
                createCelebrationFireworks();
            }

            // Show special launch notification
            if (typeof showNotification === 'function') {
                showNotification('🚀 Insurance In Bazaar is now LIVE! Welcome to the future of insurance!');
            }
        }
    }
}

// Email form handling with better UX
function initializeEmailForm() {
    document.getElementById('emailForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = this.querySelector('input[type="email"]').value;
        console.log('Email subscription:', email); // Log for potential backend integration
        const button = this.querySelector('.notify-btn');
        const originalText = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
        button.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-check"></i> Subscribed!';
            button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                button.style.background = 'linear-gradient(45deg, #ffd700, #ffed4e)';
                this.reset();
            }, 2000);
            
            // Show success message and celebration fireworks
            showNotification('🎉 Thank you! We\'ll notify you when we launch!');
            createCelebrationFireworks();
        }, 1500);
    });
}

// Notification system
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideInRight 0.5s ease-out;
        max-width: 300px;
        font-weight: bold;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-out';
        setTimeout(() => notification.remove(), 500);
    }, 4000);
}

// Create floating particles and insurance icons
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 40;
    const insuranceIcons = ['🛡️', '🏥', '🚗', '🏠', '✈️', '💼', '⚕️', '🔒', '💰', '📋', '🏦', '⚖️'];

    // Regular particles with glow effect
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        const size = Math.random() * 6 + 3;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 4) + 's';

        particlesContainer.appendChild(particle);
    }

    // Insurance-themed floating icons
    for (let i = 0; i < 12; i++) {
        const insuranceParticle = document.createElement('div');
        insuranceParticle.className = 'insurance-particle';
        insuranceParticle.textContent = insuranceIcons[i % insuranceIcons.length];
        insuranceParticle.style.left = Math.random() * 100 + '%';
        insuranceParticle.style.top = Math.random() * 100 + '%';
        insuranceParticle.style.animationDelay = Math.random() * 8 + 's';
        insuranceParticle.style.animationDuration = (Math.random() * 6 + 8) + 's';

        particlesContainer.appendChild(insuranceParticle);
    }
}

// Create geometric shapes floating in background
function createGeometricShapes() {
    const particlesContainer = document.getElementById('particles');
    const shapeCount = 8;

    for (let i = 0; i < shapeCount; i++) {
        const shape = document.createElement('div');
        shape.className = 'geometric-shape';

        const shapeType = Math.random();
        const size = Math.random() * 60 + 40;

        if (shapeType < 0.33) {
            // Circle
            shape.style.borderRadius = '50%';
        } else if (shapeType < 0.66) {
            // Square
            shape.style.borderRadius = '0';
        } else {
            // Diamond
            shape.style.borderRadius = '0';
            shape.style.transform = 'rotate(45deg)';
        }

        shape.style.width = size + 'px';
        shape.style.height = size + 'px';
        shape.style.left = Math.random() * 100 + '%';
        shape.style.animationDelay = Math.random() * 12 + 's';
        shape.style.animationDuration = (Math.random() * 8 + 12) + 's';

        particlesContainer.appendChild(shape);
    }
}

// Create floating insurance icons that move across screen
function createFloatingIcons() {
    const container = document.createElement('div');
    container.className = 'floating-icons';
    document.body.appendChild(container);

    const icons = ['🏠', '🚗', '✈️', '🏥', '💼', '🛡️', '💰', '📋'];

    function addFloatingIcon() {
        const icon = document.createElement('div');
        icon.className = 'floating-icon';
        icon.textContent = icons[Math.floor(Math.random() * icons.length)];
        icon.style.left = Math.random() * 100 + '%';
        icon.style.animationDuration = (Math.random() * 10 + 15) + 's';
        icon.style.animationDelay = Math.random() * 5 + 's';

        container.appendChild(icon);

        // Remove icon after animation
        setTimeout(() => {
            if (icon.parentNode) {
                icon.remove();
            }
        }, 20000);
    }

    // Add icons periodically
    setInterval(addFloatingIcon, 3000);

    // Add initial icons
    for (let i = 0; i < 3; i++) {
        setTimeout(addFloatingIcon, i * 1000);
    }
}

// Create wave animation at bottom
function createWaveAnimation() {
    const wave = document.createElement('div');
    wave.className = 'wave-animation';
    document.body.appendChild(wave);
}

// Create fireworks animation
function createFireworks() {
    const fireworksContainer = document.createElement('div');
    fireworksContainer.className = 'fireworks-container';
    document.body.appendChild(fireworksContainer);

    const colors = ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    const celebrationTexts = ['🎉', '✨', '🎊', '💫', '⭐', '🌟', '💥', '🎆'];

    function createSingleFirework() {
        const firework = document.createElement('div');
        firework.className = 'firework';

        const color = colors[Math.floor(Math.random() * colors.length)];
        firework.style.background = color;
        firework.style.boxShadow = `0 0 10px ${color}`;
        firework.style.left = Math.random() * 100 + '%';
        firework.style.animationDelay = Math.random() * 2 + 's';
        firework.style.animationDuration = (Math.random() * 2 + 2) + 's';

        fireworksContainer.appendChild(firework);

        // Create burst effect when firework reaches peak
        setTimeout(() => {
            createFireworkBurst(
                firework.offsetLeft + firework.offsetWidth / 2,
                window.innerHeight * 0.2,
                color
            );
        }, 2100);

        // Remove firework after animation
        setTimeout(() => {
            if (firework.parentNode) {
                firework.remove();
            }
        }, 4000);
    }

    function createFireworkBurst(x, y, color) {
        const burstCount = 12;
        const sparkleCount = 20;

        // Create main burst particles
        for (let i = 0; i < burstCount; i++) {
            const burst = document.createElement('div');
            burst.className = 'firework-burst';
            burst.style.background = color;
            burst.style.boxShadow = `0 0 15px ${color}`;
            burst.style.left = x + 'px';
            burst.style.top = y + 'px';

            const angle = (360 / burstCount) * i;
            const distance = Math.random() * 100 + 50;

            burst.style.setProperty('--angle', angle + 'deg');
            burst.style.setProperty('--distance', distance + 'px');
            burst.style.animation = `fireworkBurst 2s ease-out forwards`;
            burst.style.transform = `rotate(${angle}deg) translateY(-${distance}px)`;

            fireworksContainer.appendChild(burst);

            setTimeout(() => burst.remove(), 2000);
        }

        // Create sparkles
        for (let i = 0; i < sparkleCount; i++) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';
            sparkle.style.background = color;
            sparkle.style.boxShadow = `0 0 10px ${color}`;
            sparkle.style.left = (x + Math.random() * 200 - 100) + 'px';
            sparkle.style.top = (y + Math.random() * 200 - 100) + 'px';
            sparkle.style.animationDelay = Math.random() * 1 + 's';

            fireworksContainer.appendChild(sparkle);

            setTimeout(() => sparkle.remove(), 3000);
        }

        // Add celebration text occasionally
        if (Math.random() < 0.3) {
            const celebrationText = document.createElement('div');
            celebrationText.className = 'celebration-text';
            celebrationText.textContent = celebrationTexts[Math.floor(Math.random() * celebrationTexts.length)];
            celebrationText.style.left = (x - 20) + 'px';
            celebrationText.style.top = (y - 30) + 'px';
            celebrationText.style.color = color;

            fireworksContainer.appendChild(celebrationText);

            setTimeout(() => celebrationText.remove(), 4000);
        }
    }

    // Launch fireworks periodically
    function launchFireworks() {
        const fireworkCount = Math.random() * 3 + 1; // 1-4 fireworks

        for (let i = 0; i < fireworkCount; i++) {
            setTimeout(() => {
                createSingleFirework();
            }, i * 500);
        }
    }

    // Start fireworks show
    launchFireworks();

    // Continue launching fireworks
    setInterval(launchFireworks, Math.random() * 5000 + 3000); // Every 3-8 seconds
}

// Create special celebration fireworks for countdown milestones
function createCelebrationFireworks() {
    const colors = ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'];

    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const fireworksContainer = document.querySelector('.fireworks-container');
            if (fireworksContainer) {
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight * 0.5;
                const color = colors[i % colors.length];

                // Create multiple bursts for celebration
                for (let j = 0; j < 3; j++) {
                    setTimeout(() => {
                        createFireworkBurst(x + j * 50, y + j * 30, color);
                    }, j * 200);
                }
            }
        }, i * 300);
    }
}

// Enhanced firework burst function (moved outside for reuse)
function createFireworkBurst(x, y, color) {
    const fireworksContainer = document.querySelector('.fireworks-container');
    if (!fireworksContainer) return;

    const burstCount = 15;
    const sparkleCount = 25;

    // Create main burst particles
    for (let i = 0; i < burstCount; i++) {
        const burst = document.createElement('div');
        burst.className = 'firework-burst';
        burst.style.background = color;
        burst.style.boxShadow = `0 0 20px ${color}`;
        burst.style.left = x + 'px';
        burst.style.top = y + 'px';

        const angle = (360 / burstCount) * i;
        const distance = Math.random() * 120 + 60;

        burst.style.transform = `rotate(${angle}deg) translateY(-${distance}px)`;

        fireworksContainer.appendChild(burst);
        setTimeout(() => burst.remove(), 2000);
    }

    // Create sparkles
    for (let i = 0; i < sparkleCount; i++) {
        const sparkle = document.createElement('div');
        sparkle.className = 'sparkle';
        sparkle.style.background = color;
        sparkle.style.boxShadow = `0 0 15px ${color}`;
        sparkle.style.left = (x + Math.random() * 300 - 150) + 'px';
        sparkle.style.top = (y + Math.random() * 300 - 150) + 'px';
        sparkle.style.animationDelay = Math.random() * 1.5 + 's';

        fireworksContainer.appendChild(sparkle);
        setTimeout(() => sparkle.remove(), 3000);
    }
}

// Create single top scrolling text
function createScrollingText() {
    const insuranceMessages = [
        "🏠 Protect Your Home • 🚗 Secure Your Vehicle • 🏥 Health Coverage • ✈️ Travel Insurance • 💼 Business Protection",
        "🛡️ Life Insurance • 💰 Financial Security • 📋 Easy Claims • 🔒 Trusted Coverage • ⚡ Quick Quotes • 🌟 Best Rates",
        "🎯 Tailored Plans • 🏦 Reliable Service • 💎 Premium Benefits • 🚀 Fast Processing • 🤝 Expert Support • 24/7 Service",
        "💡 Smart Solutions • 🔐 Secure Platform • 📱 Mobile App • 🌍 Global Coverage • 💯 Satisfaction Guaranteed"
    ];

    // Create single scrolling text container at top
    const scrollingContainer = document.createElement('div');
    scrollingContainer.className = 'scrolling-text-container';
    document.body.appendChild(scrollingContainer);

    // Function to add new scrolling text
    function addScrollingText() {
        const text = document.createElement('div');
        text.className = 'scrolling-text';

        const message = insuranceMessages[Math.floor(Math.random() * insuranceMessages.length)];
        text.textContent = message;

        scrollingContainer.appendChild(text);

        // Remove after animation completes
        setTimeout(() => {
            if (text.parentNode) {
                text.remove();
            }
        }, 25000);
    }

    // Start first scrolling text immediately
    addScrollingText();

    // Continue adding new scrolling text every 12 seconds
    setInterval(addScrollingText, 12000);
}



// Create dynamic text effects for existing elements
function enhanceExistingText() {
    // Add typing effect to subtitle
    const subtitle = document.querySelector('.subtitle');
    if (subtitle) {
        const originalText = subtitle.textContent;
        subtitle.textContent = '';

        let i = 0;
        const typeWriter = setInterval(() => {
            subtitle.textContent += originalText.charAt(i);
            i++;
            if (i > originalText.length) {
                clearInterval(typeWriter);
            }
        }, 50);
    }

    // Add glow pulse to main title
    const mainTitle = document.querySelector('.main-title');
    if (mainTitle) {
        setInterval(() => {
            mainTitle.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3), 0 0 30px rgba(255,215,0,0.8)';
            setTimeout(() => {
                mainTitle.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            }, 1000);
        }, 5000);
    }
}

// Add interactive effects to insurance icons
function initializeInsuranceIcons() {
    document.querySelectorAll('.insurance-icon').forEach(icon => {
        icon.addEventListener('click', function() {
            // Reset animation
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'floatIcon 3s ease-in-out infinite';
            }, 10);
            
            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 215, 0, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width - size) / 2 + 'px';
            ripple.style.top = (rect.height - size) / 2 + 'px';
            
            this.appendChild(ripple);
            setTimeout(() => ripple.remove(), 600);
        });
    });
}

// Add hover effects for insurance icons with tooltips
function addInsuranceTooltips() {
    const tooltips = {
        'fa-heartbeat': 'Life Insurance - Protect your family\'s future',
        'fa-user-md': 'Health Insurance - Comprehensive medical coverage',
        'fa-car': 'Auto Insurance - Drive with confidence',
        'fa-home': 'Home Insurance - Secure your property',
        'fa-plane': 'Travel Insurance - Safe journeys worldwide'
    };
    
    document.querySelectorAll('.insurance-icon').forEach(icon => {
        const iconClass = icon.querySelector('i').className.split(' ').find(cls => cls.startsWith('fa-'));
        const tooltipText = tooltips[iconClass];
        
        if (tooltipText) {
            icon.addEventListener('mouseenter', function() {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = tooltipText;
                tooltip.style.cssText = `
                    position: absolute;
                    bottom: 120%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 5px;
                    font-size: 0.8rem;
                    white-space: nowrap;
                    z-index: 1000;
                    animation: fadeInUp 0.3s ease-out;
                `;
                
                this.appendChild(tooltip);
            });
            
            icon.addEventListener('mouseleave', function() {
                const tooltip = this.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        }
    });
}

// Add pulsing effect to main elements
function addPulsingEffects() {
    const logo = document.querySelector('.logo');
    const countdown = document.querySelector('.countdown');

    setInterval(() => {
        logo.style.transform = 'scale(1.05)';
        setTimeout(() => {
            logo.style.transform = 'scale(1)';
        }, 200);
    }, 4000);

    setInterval(() => {
        countdown.style.transform = 'scale(1.02)';
        setTimeout(() => {
            countdown.style.transform = 'scale(1)';
        }, 300);
    }, 6000);
}

// Create animated background elements
function createAnimatedBackground() {
    // Create multiple layers of animated elements
    const backgroundLayer = document.createElement('div');
    backgroundLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        pointer-events: none;
        overflow: hidden;
    `;
    document.body.appendChild(backgroundLayer);

    // Create moving gradient orbs
    for (let i = 0; i < 5; i++) {
        const orb = document.createElement('div');
        orb.style.cssText = `
            position: absolute;
            width: ${Math.random() * 200 + 100}px;
            height: ${Math.random() * 200 + 100}px;
            background: radial-gradient(circle, rgba(255,215,0,0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: orbFloat ${Math.random() * 20 + 20}s ease-in-out infinite;
            animation-delay: ${Math.random() * 10}s;
        `;
        orb.style.left = Math.random() * 100 + '%';
        orb.style.top = Math.random() * 100 + '%';
        backgroundLayer.appendChild(orb);
    }

    // Add CSS for orb animation
    const orbStyle = document.createElement('style');
    orbStyle.textContent = `
        @keyframes orbFloat {
            0%, 100% {
                transform: translate(0, 0) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translate(50px, -100px) scale(1.2);
                opacity: 0.6;
            }
            50% {
                transform: translate(-30px, -200px) scale(0.8);
                opacity: 0.4;
            }
            75% {
                transform: translate(-80px, -50px) scale(1.1);
                opacity: 0.7;
            }
        }
    `;
    document.head.appendChild(orbStyle);
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing countdown...');
    console.log('Launch date set to:', LAUNCH_DATE);

    // Start countdown timer immediately
    updateCountdown(); // Run once immediately
    setInterval(updateCountdown, 1000); // Then every second

    // Display launch date immediately
    const launchDateElement = document.getElementById('launchDate');
    if (launchDateElement) {
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        const launchDateString = LAUNCH_DATE.toLocaleDateString('en-US', options);
        launchDateElement.innerHTML = `
            <i class="fas fa-calendar-alt"></i>
            <span>Launching: ${launchDateString}</span>
        `;
    }

    // Initialize email form
    initializeEmailForm();

    // Create all background animations
    createParticles();
    createGeometricShapes();
    createFloatingIcons();
    createWaveAnimation();
    createAnimatedBackground();
    createFireworks();
    createScrollingText();

    // Initialize insurance icon interactions
    initializeInsuranceIcons();

    // Add tooltips
    addInsuranceTooltips();

    // Add pulsing effects
    addPulsingEffects();

    // Enhance existing text with effects
    enhanceExistingText();

    // Test countdown after a short delay to ensure all elements are loaded
    setTimeout(() => {
        console.log('Testing countdown elements...');
        const testElements = ['days', 'hours', 'minutes', 'seconds'];
        testElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                console.log(`✓ Found element: ${id}`);
            } else {
                console.error(`✗ Missing element: ${id}`);
            }
        });

        // Force update countdown
        updateCountdown();
    }, 500);

    // Add click events to countdown for fireworks
    document.querySelectorAll('.countdown-item').forEach(item => {
        item.addEventListener('click', function() {
            createCelebrationFireworks();
            this.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 300);
        });
    });
    
    // Add some performance optimizations
    // Reduce particles on mobile devices
    if (window.innerWidth < 768) {
        const particles = document.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            if (index > 20) { // Keep only first 20 particles on mobile
                particle.remove();
            }
        });

        // Reduce geometric shapes on mobile
        const shapes = document.querySelectorAll('.geometric-shape');
        shapes.forEach((shape, index) => {
            if (index > 3) {
                shape.remove();
            }
        });
    }
    
    // Add smooth scrolling for any future anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Handle window resize for responsive adjustments
window.addEventListener('resize', function() {
    // Adjust particle count based on screen size
    const particles = document.querySelectorAll('.particle');
    const shouldShowAllParticles = window.innerWidth >= 768;
    
    particles.forEach((particle, index) => {
        if (!shouldShowAllParticles && index > 15) {
            particle.style.display = 'none';
        } else {
            particle.style.display = 'block';
        }
    });
});

// Add keyboard navigation support
document.addEventListener('keydown', function(e) {
    // Press 'E' to focus email input
    if (e.key === 'e' || e.key === 'E') {
        const emailInput = document.querySelector('.email-input');
        if (emailInput && document.activeElement !== emailInput) {
            emailInput.focus();
            e.preventDefault();
        }
    }
    
    // Press 'Escape' to clear focus
    if (e.key === 'Escape') {
        document.activeElement.blur();
    }
});
