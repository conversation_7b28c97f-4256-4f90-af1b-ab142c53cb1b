/* Insurance In Bazaar - Coming Soon Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4a90e2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    overflow-x: hidden;
    position: relative;
    animation: backgroundShift 20s ease-in-out infinite;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(74, 144, 226, 0.2) 0%, transparent 50%);
    animation: backgroundPulse 15s ease-in-out infinite;
    z-index: 0;
}

.container {
    text-align: center;
    max-width: 90%;
    width: 100%;
    max-width: 800px;
    padding: 1rem;
    position: relative;
    z-index: 2;
}

.logo {
    font-size: clamp(1.8rem, 4vw, 2.8rem);
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    color: #ffd700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    animation: logoGlow 2s ease-in-out infinite alternate;
}

.logo i {
    font-size: clamp(1.5rem, 3.5vw, 2.5rem);
    animation: shieldPulse 2s ease-in-out infinite;
}

.main-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 300;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
    line-height: 1.2;
}

.subtitle {
    font-size: clamp(1rem, 2.5vw, 1.3rem);
    margin-bottom: 1.5rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.3s both;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}

.launch-date {
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.35s both;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
}

.launch-date i {
    animation: pulse 2s ease-in-out infinite;
}

.launch-date span {
    animation: glow 3s ease-in-out infinite alternate;
}

.insurance-icons {
    display: flex;
    justify-content: center;
    gap: clamp(1rem, 4vw, 2rem);
    margin: 2rem 0;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.insurance-icon {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    width: clamp(60px, 12vw, 80px);
    height: clamp(60px, 12vw, 80px);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    animation: floatIcon 3s ease-in-out infinite;
    transition: all 0.3s ease;
    cursor: pointer;
}

.insurance-icon:nth-child(2) { animation-delay: 0.5s; }
.insurance-icon:nth-child(3) { animation-delay: 1s; }
.insurance-icon:nth-child(4) { animation-delay: 1.5s; }
.insurance-icon:nth-child(5) { animation-delay: 2s; }

.insurance-icon:hover {
    transform: scale(1.1);
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
}

.insurance-icon i {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: #ffd700;
}

.countdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: clamp(0.5rem, 2vw, 1.5rem);
    margin: 3rem auto;
    max-width: 500px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.countdown-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: clamp(1rem, 3vw, 1.5rem) clamp(0.5rem, 2vw, 1rem);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    animation: countdownPulse 2s ease-in-out infinite;
}

.countdown-item:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.countdown-number {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: bold;
    display: block;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    transition: all 0.2s ease;
}

.countdown-label {
    font-size: clamp(0.7rem, 1.5vw, 0.9rem);
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
}

.email-signup {
    margin: 3rem auto;
    animation: fadeInUp 1s ease-out 0.9s both;
    max-width: 500px;
}

.email-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.email-input {
    padding: clamp(0.8rem, 2vw, 1.2rem);
    border: none;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    color: white;
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.email-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.email-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    transform: scale(1.02);
}

.notify-btn {
    padding: clamp(0.8rem, 2vw, 1.2rem) clamp(1.5rem, 4vw, 2.5rem);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border: none;
    border-radius: 50px;
    color: #1e3c72;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.notify-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
}

.notify-btn:active {
    transform: translateY(-1px);
}

.notify-btn:disabled {
    cursor: not-allowed;
    opacity: 0.8;
}

.social-links {
    margin-top: 3rem;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.social-links a {
    color: white;
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    margin: 0 clamp(0.5rem, 2vw, 1rem);
    text-decoration: none;
    transition: all 0.3s ease;
    opacity: 0.8;
    display: inline-block;
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-links a:hover {
    opacity: 1;
    transform: translateY(-3px) scale(1.1);
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    color: #ffd700;
}

.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.insurance-particle {
    position: absolute;
    color: rgba(255, 215, 0, 0.6);
    font-size: 1.8rem;
    animation: insuranceFloat 8s ease-in-out infinite;
    pointer-events: none;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.geometric-shape {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.1);
    animation: geometricFloat 12s linear infinite;
    pointer-events: none;
}

.wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(90deg,
        rgba(255, 215, 0, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 215, 0, 0.1) 100%);
    animation: waveMove 8s ease-in-out infinite;
    z-index: 1;
}

.floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    color: rgba(255, 215, 0, 0.3);
    animation: floatingIconMove 15s linear infinite;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
}

.fireworks-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}

.firework {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    animation: fireworkLaunch 3s ease-out infinite;
}

.firework::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: fireworkGlow 3s ease-out infinite;
}

.firework-burst {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: fireworkBurst 2s ease-out forwards;
}

.firework-trail {
    position: absolute;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, transparent, currentColor);
    animation: fireworkTrail 1.5s ease-out infinite;
}

.sparkle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #ffd700;
    border-radius: 50%;
    animation: sparkleAnimation 2s ease-out infinite;
    box-shadow: 0 0 10px currentColor;
}

.celebration-text {
    position: absolute;
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    animation: celebrationFloat 4s ease-out infinite;
    pointer-events: none;
    z-index: 2;
}

.scrolling-text-container {
    position: absolute;
    top: 8%;
    width: 100%;
    height: 50px;
    overflow: hidden;
    z-index: 1;
    pointer-events: none;
    background: linear-gradient(90deg,
        rgba(255, 215, 0, 0.1) 0%,
        rgba(255, 215, 0, 0.05) 50%,
        rgba(255, 215, 0, 0.1) 100%);
    border-top: 1px solid rgba(255, 215, 0, 0.3);
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.scrolling-text {
    position: absolute;
    white-space: nowrap;
    font-size: clamp(1.1rem, 2.5vw, 1.6rem);
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    animation: scrollText 25s linear infinite;
    padding: 0 2rem;
    line-height: 50px;
    display: flex;
    align-items: center;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes logoGlow {
    0% { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,215,0,0.3); }
    100% { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,215,0,0.6); }
}

@keyframes shieldPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes countdownPulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255,215,0,0.4); }
    50% { box-shadow: 0 0 0 10px rgba(255,215,0,0); }
}

@keyframes insuranceFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-40px) rotate(90deg) scale(1.2);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-80px) rotate(180deg) scale(1);
        opacity: 0.6;
    }
    75% {
        transform: translateY(-40px) rotate(270deg) scale(1.2);
        opacity: 0.8;
    }
}

@keyframes backgroundShift {
    0%, 100% {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4a90e2 100%);
    }
    25% {
        background: linear-gradient(135deg, #2a5298 0%, #4a90e2 50%, #1e3c72 100%);
    }
    50% {
        background: linear-gradient(135deg, #4a90e2 0%, #1e3c72 50%, #2a5298 100%);
    }
    75% {
        background: linear-gradient(135deg, #1e3c72 0%, #4a90e2 50%, #2a5298 100%);
    }
}

@keyframes backgroundPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

@keyframes geometricFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10%, 90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes waveMove {
    0%, 100% {
        transform: translateX(0) scaleY(1);
        opacity: 0.3;
    }
    50% {
        transform: translateX(-50px) scaleY(1.2);
        opacity: 0.6;
    }
}

@keyframes floatingIconMove {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10%, 90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes fireworkLaunch {
    0% {
        transform: translateY(100vh) scale(1);
        opacity: 1;
        background: #ffd700;
        box-shadow: 0 0 10px #ffd700;
    }
    70% {
        transform: translateY(20vh) scale(1.2);
        opacity: 1;
        background: #ff6b6b;
        box-shadow: 0 0 20px #ff6b6b;
    }
    100% {
        transform: translateY(15vh) scale(0);
        opacity: 0;
        background: #4ecdc4;
        box-shadow: 0 0 30px #4ecdc4;
    }
}

@keyframes fireworkGlow {
    0% {
        background: radial-gradient(circle, #ffd700 0%, transparent 50%);
        transform: scale(1);
    }
    70% {
        background: radial-gradient(circle, #ff6b6b 0%, transparent 70%);
        transform: scale(3);
    }
    100% {
        background: radial-gradient(circle, #4ecdc4 0%, transparent 100%);
        transform: scale(5);
        opacity: 0;
    }
}

@keyframes fireworkBurst {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(2) rotate(360deg);
        opacity: 0;
    }
}

@keyframes fireworkTrail {
    0% {
        transform: translateY(0) scaleY(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scaleY(0.3);
        opacity: 0;
    }
}

@keyframes sparkleAnimation {
    0%, 100% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 1;
    }
}

@keyframes celebrationFloat {
    0% {
        transform: translateY(0) scale(0.8);
        opacity: 0;
    }
    25% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
    75% {
        transform: translateY(-40px) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-60px) scale(0.8);
        opacity: 0;
    }
}

@keyframes scrollText {
    0% {
        transform: translateX(100vw);
    }
    100% {
        transform: translateX(-100%);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

@keyframes glow {
    0% {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    }
    100% {
        text-shadow: 0 0 20px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8);
    }
}

/* Notification animations */
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

/* Media Queries */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }
    
    .insurance-icons {
        gap: 1rem;
    }
    
    .insurance-icon {
        width: 60px;
        height: 60px;
    }
    
    .countdown {
        gap: 0.5rem;
        margin: 2rem auto;
    }
    
    .social-links a {
        margin: 0 0.3rem;
    }
}

@media (max-width: 480px) {
    .countdown {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .insurance-icons {
        justify-content: space-around;
    }
    
    .logo {
        flex-direction: column;
        gap: 0.3rem;
    }
}
