<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('policy_pricings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('policy_id')->constrained()->onUpdate('cascade')->onDelete('cascade');
            $table->foreignId('duration_id')->constrained('durations')->onUpdate('cascade')->onDelete('cascade');
            $table->decimal('price', 10, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('policy_pricings');
    }
};
