<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('insurance_nominees', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->foreignId('insurance_id')
                ->constrained('insurances')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->date('dob')->nullable();
            $table->decimal('percentage')->nullable();
            $table->string('relation')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('insurance_nominees');
    }
};
