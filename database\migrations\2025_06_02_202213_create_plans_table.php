<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->tinyInteger('interval');
            $table->decimal('amount', 8, 2);
            $table->integer('user_limit')->nullable();
            $table->integer('customer_limit')->nullable();
            $table->integer('agent_limit')->nullable();
            $table->tinyInteger('is_show_history');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
