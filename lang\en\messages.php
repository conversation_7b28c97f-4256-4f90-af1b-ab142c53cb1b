<?php

return [
    'or_continue' => 'Or continue',
    'common' => [
        'chart' => 'Chart',
        'income' => 'Income',
        'filter_options' => 'Filter Options',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'closed' => 'Closed',
        'count' => 'Count',
        'save' => 'Save',
        'send' => 'Send',
        'submit' => 'Submit',
        'cancel' => 'Cancel',
        'confirm' => 'Confirm',
        'discard' => 'Discard',
        'country' => 'Country',
        'state' => 'State',
        'city' => 'City',
        'please_wait' => 'Please wait...',
        'back' => 'Back',
        'action' => 'Action',
        'add' => 'Add',
        'edit' => 'Edit',
        'name' => 'Name',
        'details' => 'Details',
        'service' => 'Service',
        'select_language' => 'Select Language',
        'language' => 'Language',
        'title' => 'Title',
        'slug' => 'Slug',
        'created_by' => 'Created by',
        'read' => 'Read',
        'select_an_option' => 'Options',
        'leave_a_message' => 'Leave a Message',
        'contact_us' => 'Contact Us',
        'no_data_available' => 'No items found. Try to broaden your search.',
        'allowed_types' => 'Allowed file types: png, jpg, jpeg.',
        'select_category' => 'Select Category',
        'select_subcategory' => 'Select Sub Category',
        'delete_warning' => 'Are you sure want to delete this',
        'delete' => 'Yes, Delete',
        'cancel_delete' => 'No, Cancel',
        'deleted' => 'Deleted!',
        'delete_message' => 'has been deleted',
        'contact' => 'Contact',
        'image_warning' => 'is not valid! Allowed file types: png, jpg, jpeg.',
        'success_msg' => 'Message has been sent successfully.',
        'change_image' => 'Change image',
        'cancel_image' => 'Cancel image',
        'remove_image' => 'Remove image',
        'change_profile' => 'Change profile',
        'cancel_profile' => 'Cancel profile',
        'change_logo' => 'Change logo',
        'change_favicon' => 'Change favicon',
        'showing' => 'Showing',
        'result' => 'Results',
        'time' => 'Time',
        'created_at' => 'Created At',
        'to' => 'To',
        'of' => 'Of',
        'required' => 'The :attribute field is required.',
        'max' => 'The :attribute must not be greater than :max.',
        'login' => 'Login',
        'all_rights' => 'All Rights Reserved',
        'get_the_best' => 'Get The Best Blog Stories Into Your Inbox!',
        'by' => 'By',
        'subscribe' => 'Subscribe',
        'your_email' => 'Your Email',
        'images' => 'Images :',
        'files' => 'Files :',
        'tags' => 'Tags :',
        'january' => 'January',
        'february' => 'February',
        'march' => 'March',
        'april' => 'April',
        'may' => 'May',
        'june' => 'June',
        'july' => 'July',
        'august' => 'August',
        'september' => 'September',
        'october' => 'October',
        'november' => 'November',
        'december' => 'December',
        'jan' => 'Jan',
        'feb' => 'Feb',
        'mar' => 'Mar',
        'apr' => 'Apr',
        'jun' => 'Jun',
        'jul' => 'Jul',
        'aug' => 'Aug',
        'sep' => 'Sep',
        'oct' => 'Oct',
        'nov' => 'Nov',
        'dec' => 'Dec',
        'apply' => 'Apply',
        'custom' => 'Custom',
        'note' => 'Note',
        'image_error' => 'The image must be a file of type: png, jpg, jpeg, webp, svg.',
        'video_error' => 'The image must be a file of type: mp4, mov, mkv, webm, avi.',
        'update' => 'Update',
        'show_contact' => 'Show Contact Details',
        'create_an_account' => 'Create an Account',
        'already_have_an_account' => 'Already have an Account?',
        'sign_in_here' => 'Sign in here',
        'new_here' => 'New Here',
        'register' => 'Register',
        'send_email' => 'Send Email',
        'approval_status' => 'Approval Status',
        'rejected' => 'Rejected',
        'approved' => 'Approved',
        'view' => 'View',
        'on' => 'On',
        'off' => 'Off',
        'declined' => 'Declined',
        'allow_cookies' => 'Allow Cookies',
        'change_cover_image' => 'Change Cover Image',
        'image_error_excel' => 'The image must be a file of type: png, jpg, jpeg, pdf,excel',
        'pending' => 'Pending',
        'received' => 'Received',
        'are_you_sure' => 'Are you sure you would like to do this?',
        'sign_in' => 'Sign In',
        'sign_up_for_an_account' => 'Sign up for an account',
        'remember_me' => 'Remember Me',
        'null' => 'Null',
        'remembered_password' => 'Remembered Password?',
        'back_to_login' => 'Back to Login',
        'email_varification' => 'Email Verified Successfully',
        'email_not_verified' => 'Email Not Verified, Please Verify Your Email first.',
        'dont_have_an_account' => 'Don\'t have an account?',
        'sign_up' => 'Sign Up',
    ],
    'user' => [
        'user' => 'User',
        'profile_details' => 'Profile Details',
        'avatar' => 'Avatar',
        'full_name' => 'Full Name',
        'email' => 'Email', // Used for label and placeholder
        'contact_number' => 'Contact Number',
        'save_changes' => 'Save Changes',
        'setting' => 'Setting',
        'account_setting' => 'Account Settings',
        'change_password' => 'Change Password', // General term for "change password" functionality
        'current_password' => 'Current Password', // Specific field label
        'new_password' => 'New Password', // Specific field label
        'password' => 'Password', // For the password input field label & placeholder
        'confirm_password' => 'Confirm Password', // Used for label and placeholder
        'account' => 'Account',
        'staff_details' => 'Staff Details',
        'gender' => 'Gender', // Label for select
        'profile' => 'Profile', // Label for file upload & table column
        'user_information' => 'User Information', // Section title
        'name' => 'Name', // Label and placeholder for name input & table column
        'phone' => 'Phone', // Label for phone input & table column
        'invalid_phone_number' => 'The phone number is not valid', // Validation message
        'role' => 'Role', // Label for select, placeholder for select & table column
        'created_at' => 'Created At', // Table column
        'actions' => 'Actions', // Table column
        'cannot_delete_yourself' => 'Cannot delete yourself!', // Notification
        'user_deleted_successfully' => 'User Deleted Successfully!',
        'user_created_successfully' => 'User Created Successfully.',
        'user_updated_successfully' => 'User Updated Successfully.',
        'users_list_title' => 'Users',
        'create_user_title' => 'Create User',
        'edit_user_title' => 'Edit User',
    ],
    'agent' => [
        'agents' => 'Agents',
        'name' => 'Name',
        'email' => 'Email',
        'phone_number' => 'Phone Number',
        'profile' => 'Profile',
        'city' => 'City',
        'state' => 'State',
        'country' => 'Country',
        'zip' => 'Zip',
        'address' => 'Address',
        'additional_details' => 'Additional Details',
        'company_name' => 'Company Name',
        'tax_number' => 'Tax Number',
        'note' => 'Note',
        'agent_deleted_successfully' => 'Agent Deleted Successfully!',
        'agent_created_successfully' => 'Agent Created Successfully.',
        'create_agent_title' => 'Create Agent',
        'edit_agent_title' => 'Edit Agent',
        'agent_updated_successfully' => 'Agent Updated Successfully.',
        'agents_list_title' => 'Agents',
    ],
    'customer' => [
        'customers' => 'Customers',
        'name' => 'Name',
        'email' => 'Email',
        'phone_number' => 'Phone Number',
        'gender' => 'Gender',
        'city' => 'City',
        'state' => 'State',
        'country' => 'Country',
        'zip' => 'Zip',
        'profile' => 'Profile',
        'address' => 'Address',
        'additional_details' => 'Additional Details',
        'company_name' => 'Company Name',
        'tax_number' => 'Tax Number',
        'dob' => 'Date of Birth',
        'age' => 'Age',
        'marital_status' => 'Marital Status',
        'blood_group' => 'Blood Group',
        'height' => 'Height',
        'weight' => 'Weight',
        'note' => 'Note',
        'policy_assigned' => 'Policy Assigned',
        'customer_deleted_successfully' => 'Customer Deleted Successfully!',
        'create_customer_title' => 'Create Customer',
        'customer_created_successfully' => 'Customer Created Successfully.',
        'list_customers_title' => 'Customers',
        'edit_customer_title' => 'Edit Customer',
        'customer_updated_successfully' => 'Customer Updated Successfully.',
        'edit_customer_title' => 'Edit Customer',
        'customer_updated_successfully' => 'Customer Updated Successfully.',
        'edit_customer_title' => 'Edit Customer',
        'customer_updated_successfully' => 'Customer Updated Successfully.',
    ],
    'document_type' => [
        'name' => 'Name',
        'document_type' => 'Document Type',
        'document_types' => 'Document Types',
        'document_type_created_successfully' => 'Document Type Created Successfully.',
        'document_type_updated_successfully' => 'Document Type Updated Successfully.',
        'document_type_deleted_successfully' => 'Document Type Deleted Successfully.',
        'document_type' => 'Document Type',
        'document_types' => 'Document Types',
        'manage_document_types_title' => 'Document Types',
        'new_document_type' => 'New Document Type',
    ],
    'duration' => [
        'duration_terms' => 'Duration Terms',
        'duration_in_months' => 'Duration In Months',
        'duration_updated_successfully' => 'Duration Updated Successfully.',
        'duration_deleted_successfully' => 'Duration Deleted Successfully.',
        'duration' => 'Duration',
        'durations' => 'Durations',
        'manage_durations_title' => 'Manage Durations',
        'duration_created_successfully' => 'Duration Created Successfully.',
        'new_duration' => 'New Duration',
    ],
    'insurance' => [
        'insurance' => 'Insurance',
        'insurances' => 'Insurances',
        'customer' => 'Customer',
        'email' => 'Email',
        'phone_number' => 'Phone Number',
        'address' => 'Address',
        'policy_details' => 'Policy Details',
        'policy' => 'Policy',
        'policy_type' => 'Policy Type',
        'policy_sub_type' => 'Policy Sub Type',
        'sum_assured' => 'Sum Assured',
        'liability_risk' => 'Liability Risk',
        'coverage_type' => 'Coverage Type',
        'total_insured_person' => 'Total Insured Person',
        'policy_pricing' => 'Policy Pricing',
        'agent_details' => 'Agent Details',
        'agent' => 'Agent',
        'agent_commission' => 'Agent Commission',
        'other_information' => 'Other Information',
        'start_date' => 'Start Date',
        'status' => 'Status',
        'note' => 'Note',
        'installment_preview' => 'Installment Preview',
        'order_no' => 'Order No',
        'amount' => 'Amount',
        'policy_holder' => 'Policy Holder',
        'expiry_date' => 'Expiry Date',
        'create_insurance_title' => 'Create Insurance',
        'insurance_created_successfully' => 'Insurance Created Successfully.',
        'edit_insurance_title' => 'Edit Insurance',
        'insurance_updated_successfully' => 'Insurance Updated Successfully.',
        'list_insurances_title' => 'Insurances',
        'view_insurance_title' => 'View Insurance',
        'download_insurance' => 'Download Insurance',
        'customer_id' => 'Customer ID',
        'installment' => 'Installment',
        'insured' => 'Insured',
        'nominee' => 'Nominee',
        'document' => 'Document',
        'payment' => 'Payment',
        'insurance_deleted_successfully' => 'Insurance Deleted Successfully',
    ],
    'payment' => [
        'payments' => 'Payments',
        'customer' => 'Customer',
        'payment_date' => 'Payment Date',
        'insurance' => 'Insurance',
        'tax' => 'Tax',
        'premium' => 'Premium',
        'manage_payments_title' => 'Manage Payments',
        'total' => 'Total',
    ],
    'policy' => [
        'policy' => 'Policy',
        'policies' => 'Policies',
        'policy_information' => 'Policy Information',
        'title' => 'Title',
        'enter_policy_title' => 'Enter policy title',
        'policy_type' => 'Policy Type',
        'select_policy_type' => 'Select policy type',
        'policy_sub_type' => 'Policy Sub Type',
        'coverage_type' => 'Coverage Type',
        'total_insured_person' => 'Total Insured Person',
        'enter_total_insured_person' => 'Enter total insured person',
        'liability_risk' => 'Liability Risk',
        'sum_assured' => 'Sum Assured',
        'enter_sum_assured' => 'Enter sum assured',
        'policy_document_type' => 'Policy Document Type',
        'claim_document_type' => 'Claim Document Type',
        'tax' => 'Tax',
        'policy_pricing' => 'Policy Pricing',
        // Reusing 'duration_terms' and 'duration_in_months' from 'duration' array
        'price' => 'Price',
        'policy_terms' => 'Policy Terms', // Wizard Step
        'policy_description' => 'Policy Description',
        'enter_policy_description' => 'Enter policy description',
        'enter_policy_terms' => 'Enter policy terms',
        'create_policy_title' => 'Create Policy',
        'policy_created_successfully' => 'Policy Created Successfully.',
        'edit_policy_title' => 'Edit Policy',
        'policy_updated_successfully' => 'Policy Updated Successfully.',
        'list_policies_title' => 'Policies',
        'policy_deleted_successfully' => 'Policy Deleted Successfully.',
        'hint_info' => 'Select the durations you want to offer and set competitive pricing',
        'toggle_label' => 'Enable this duration',
        'toggle_helper' => 'Toggle to include this duration option',
        'notification_delete_failed' => "This Policy's Pricing has already been used in Insurance, so you can't delete it",
        'notification_update_success' => 'Other fields are updated successfully',
    ],
    'policy_sub_type' => [
        'policy_sub_type' => 'Policy Sub Type',
        'policy_sub_types' => 'Policy Sub Types',
        'name' => 'Name',
        'policy_type' => 'Policy Type',
        'select_policy_type' => 'Select Policy Type',
        'policy_sub_type_updated_successfully' => 'Policy Sub Type Updated Successfully.',
        'policy_sub_type_deleted_successfully' => 'Policy Sub Type Deleted Successfully.',
        'manage_policy_sub_types_title' => 'Policy Sub Types',
        'new_policy_sub_type' => 'New Policy Sub Type',
        'policy_sub_type_created_successfully' => 'Policy Sub Type Created Successfully.',
    ],
    'policy_type' => [
        'policy_type' => 'Policy Type',
        'policy_types' => 'Policy Types',
        'name' => 'Name',
        'policy_type_updated_successfully' => 'Policy Type Updated Successfully.',
        'manage_policy_types_title' => 'Policy Types',
        'policy_type_created_successfully' => 'Policy Type Created Successfully.',
        'new_policy_type' => 'New Policy Type',
        'policy_type_deleted_successfully' => 'Policy Type Deleted Successfully.',
    ],
    'tax' => [
        'tax' => 'Tax',
        'taxes' => 'Taxes',
        'tax_rate' => 'Tax Rate (%)',
        'tax_updated_successfully' => 'Tax Updated Successfully.',
        'tax_deleted_successfully' => 'Tax Deleted Successfully.',
        'manage_taxes_title' => 'Manage Taxes',
        'tax_created_successfully' => 'Tax Created Successfully.',
        'new_tax' => 'New Tax',
    ],
    'subscription_plan' => [
        'title' => 'Subscription Plans',
        'monthly' => 'Monthly',
        'yearly' => 'Yearly',
        'current_plan' => 'Current Plan',
        'choose_payment_method' => 'Choose Payment Method',
        'save_price' => 'Save :price',
        'email_support' => 'Email support',
        'basic_analytics' => 'Basic analytics',
        'standard_security' => 'Standard security',
        'priority_support' => 'Priority support',
        'advanced_analytics' => 'Advanced analytics',
        'api_access' => 'API access',
        'team_collaboration' => 'Team collaboration',
        'dedicated_support' => 'Dedicated support',
        'custom_integrations' => 'Custom integrations',
        'advanced_security' => 'Advanced security',
        'sla_guarantee' => 'SLA guarantee',
        'white_label_options' => 'White-label options',
        'premium_support' => 'Premium support',
        'advanced_reporting' => 'Advanced reporting',
        'custom_workflows' => 'Custom workflows',
        'enterprise_integrations' => 'Enterprise integrations',
        'standard_support' => 'Standard support',
        'basic_features' => 'Basic features',
        'description_bronze' => 'Perfect for small teams getting started',
        'description_gold' => 'Best for growing businesses and teams',
        'description_diamond' => 'Premium solution for large organizations',
        'description_plat' => 'Enterprise-grade solution with advanced features',
        'description_default' => 'A great plan for your business needs',
        'selected_plan' => 'Selected Plan',
        'unknown_plan' => 'Unknown Plan',
        'month' => 'Month',
        'feature' => 'Feature',
        'plan_limits' => 'Plan Limits',
        'no_plans_available_title' => 'No Plans Available',
        'no_plans_available_body' => 'No :tab subscription plans are currently available.',
    ],
    'claim' => [
        'claim' => 'Claim',
        'claim_number' => 'Claim Number',
        'enter_claim_number' => 'Enter Claim Number',
        'claim_date' => 'Claim Date',
        'customer' => 'Customer',
        'select_customer' => 'Select Customer',
        'insurance' => 'Insurance',
        'select_insurance' => 'Select Insurance',
        'status' => 'Status',
        'reason' => 'Reason',
        'enter_reason_for_claim' => 'Enter Reason for Claim',
        'note' => 'Note',
        'enter_additional_note' => 'Enter Additional Note',
        'claim_status_updated_successfully' => 'Claim Status Updated Successfully',
        'claim_number_already_exist' => 'Claim Number already exists',
        'claim_updated_successfully' => 'Claim Updated Successfully',
        'claim_deleted_successfully' => 'Claim Deleted Successfully',
        'manage_claims_title' => 'Claims', // Updated as per feedback
        'new_claim' => 'New Claim',
        'claim_created_successfully' => 'Claim Created Successfully',
        'view_claim_title' => 'View Claim',
        'download_claim' => 'Download Claim',
        'policy_holder' => 'Policy Holder',
        'policy_holder_details' => 'Policy Holder Details',
        'customer_id' => 'Customer ID',
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'company_name' => 'Company Name',
        'dob' => 'Date of Birth',
        'age' => 'Age',
        'gender' => 'Gender',
        'marital_status' => 'Marital Status',
        'blood_group' => 'Blood Group',
        'height' => 'Height',
        'weight' => 'Weight',
        'tax_number' => 'Tax Number',
        'address' => 'Address',
        'document' => 'Document',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
    ],
    'claim_document' => [
        'documents_heading' => 'Documents',
        'notification' => [
            'created_successfully' => 'Document Created Successfully',
            'deleted_successfully' => 'Document Deleted Successfully',
        ],
        'modal' => [
            'new_document_heading' => 'New Document',
        ],
        'button' => [
            'add_document' => 'Add Document',
        ],
        'table' => [
            'file' => 'File',
            'document_type' => 'Document Type',
            'status' => 'Status',
            'actions' => 'Actions',
        ],
        'form' => [
            'document_type' => 'Document Type',
            'file' => 'File',
            'status' => 'Status',
        ],
    ],
    'insurance_document' => [
        'documents_heading' => 'Documents',
        'notification' => [
            'created_successfully' => 'Document Created Successfully',
            'deleted_successfully' => 'Document Deleted Successfully',
        ],
        'modal' => [
            'new_document_heading' => 'New Document',
        ],
        'button' => [
            'add_document' => 'Add Document',
        ],
        'table' => [
            'file' => 'File',
            'document_type' => 'Document Type',
            'status' => 'Status',
            'actions' => 'Actions',
        ],
        'form' => [
            'document_type' => 'Document Type',
            'file' => 'File',
            'status' => 'Status',
        ],
    ],
    'insurance_installment' => [
        'table' => [
            'order_no' => 'Order No',
            'amount' => 'Amount',
            'start_date' => 'Start Date',
            'policy' => 'Policy',
        ],
    ],
    'insurance_insured' => [
        'insured_heading' => 'Insured',
        'notification' => [
            'created_successfully' => 'Insured Created Successfully',
            'deleted_successfully' => 'Insured Deleted Successfully',
        ],
        'modal' => [
            'new_insured_heading' => 'New Insured',
        ],
        'button' => [
            'add_insured' => 'Add Insured',
        ],
        'table' => [
            'name' => 'Name',
            'dob' => 'Date of Birth',
            'age' => 'Age',
            'gender' => 'Gender',
            'blood_group' => 'Blood Group',
            'height' => 'Height',
            'weight' => 'Weight',
            'relation' => 'Relation',
            'actions' => 'Actions',
        ],
        'form' => [
            'name' => 'Name',
            'dob' => 'Date of Birth',
            'age' => 'Age',
            'gender' => 'Gender',
            'blood_group' => 'Blood Group',
            'height' => 'Height',
            'weight' => 'Weight',
            'relation' => 'Relation',
            'placeholder' => [
                'name' => 'Enter insured name',
                'dob' => 'Select date of birth',
                'age' => 'Enter age',
                'blood_group' => 'Enter blood group',
                'height' => 'Enter height',
                'weight' => 'Enter weight',
                'relation' => 'Enter relation',
            ],
        ],
    ],
    'insurance_nominee' => [
        'nominees_heading' => 'Nominees',
        'notification' => [
            'created_successfully' => 'Nominee Created Successfully',
            'deleted_successfully' => 'Nominee Deleted Successfully',
        ],
        'modal' => [
            'new_nominee_heading' => 'New Nominee',
        ],
        'button' => [
            'add_nominee' => 'Add Nominee',
        ],
        'table' => [
            'name' => 'Name',
            'dob' => 'Date of Birth',
            'percentage' => 'Percentage',
            'relation' => 'Relation',
            'actions' => 'Actions',
        ],
        'form' => [
            'name' => 'Name',
            'dob' => 'Date of Birth',
            'percentage' => 'Percentage',
            'relation' => 'Relation',
            'placeholder' => [
                'name' => 'Enter nominee name',
                'dob' => 'Select date of birth',
                'percentage' => 'Enter percentage',
                'relation' => 'Enter relation',
            ],
        ],
    ],
    'insurance_payment' => [
        'payments_heading' => 'Payments',
        'notification' => [
            'created_successfully' => 'Payment Created Successfully',
            'deleted_successfully' => 'Payment Deleted Successfully',
        ],
        'modal' => [
            'new_payment_heading' => 'New Payment',
        ],
        'button' => [
            'add_payment' => 'Add Payment',
        ],
        'table' => [
            'payment_date' => 'Payment Date',
            'amount' => 'Amount',
            'status' => 'Status',
            'installment' => 'Installment',
            'actions' => 'Actions',
        ],
        'form' => [
            'payment_date' => 'Payment Date',
            'amount' => 'Amount',
            'status' => 'Status',
            'installment' => 'Installment',
            'placeholder' => [
                'amount' => 'Enter Amount',
                'installment' => 'Select Installment',
            ],
            'hint' => [
                'info' => 'INFO',
                'na' => 'N/A',
                'to_pay' => 'to Pay',
            ],
        ],
    ],
    'setting' => [
        'general_settings' => 'General Settings',
        'settings' => 'Settings',
        'name' => 'Name',
        'enter_app_name' => 'Enter the name of your application',
        'email' => 'Email',
        'enter_app_email' => 'Enter the email address for your application',
        'phone' => 'Phone',
        'address' => 'Address',
        'enter_address' => 'Enter the address',
        'currency_icon' => 'Currency Icon',
        'timezone' => 'Timezone',
        'invoice_customer_prefix' => 'Invoice Customer Prefix',
        'enter_invoice_customer_prefix' => 'Enter the prefix for customer invoices',
        'agent_number_prefix' => 'Agent Number Prefix',
        'enter_agent_number_prefix' => 'Enter the prefix for agent numbers',
        'insurance_customer_prefix' => 'Insurance Customer Prefix',
        'enter_insurance_customer_prefix' => 'Enter the prefix for insurance customers',
        'claim_number_prefix' => 'Claim Number Prefix',
        'enter_claim_number_prefix' => 'Enter the prefix for claim numbers',
        'settings_updated_successfully' => 'Settings Updated Successfully.',
        // 'setting' => 'Setting',
        'general' => 'General',
        'contact_information' => 'Contact Information',
        'contact_address' => 'Contact Address',
        'currency_settings' => 'Currency Settings',
        'general_details' => 'General Details',
        'currency' => 'Currency',
        'prefix' => 'Prefix',
        'address' => 'Address',
        'postal_code' => 'Postal Code',
        'site_key' => 'Site Key',
        'secret_key' => 'Secret Key',
        'google_recaptcha' => 'Google Recaptcha',
        'show_captcha' => 'Show Recaptcha',
        'show_capcha_register' => 'Show Captcha On Registration',
        'social_media_setting' => 'Social Media Settings',
        'logo' => 'Logo',
        'favicon' => 'Favicon',
        'facebook_url' => 'Facebook URL',
        'twitter_url' => 'Twitter URL',
        'instagram_url' => 'Instagram URL',
        'pinterest_url' => 'Pinterest URL',
        'linkedin_url' => 'LinkedIn URL',
        'vk_url' => 'VK URL',
        'telegram_url' => 'Telegram URL',
        'youtube_url' => 'Youtube URL',
        'cookie_warning' => 'Cookie Warnings',
        'show_cookie_warning' => 'Show Cookie Warnings',
        'copy_right_text' => 'Copyright Text',
        'about_text' => 'About Text',
        'cms' => 'CMS',
        'terms-conditions' => 'Terms & conditions',
        'support' => 'Support',
        'privacy' => 'Privacy',
        'app_name' => 'App Name',
        'front_language' => 'Default Front Language',
        'invalid_youtube_url' => 'Please enter a valid Youtube URL',
        'invalid_facebook_url' => 'Please enter a valid Facebook URL',
        'invalid_instagram_url' => 'Please enter a valid Instagram URL',
        'invalid_twitter_url' => 'Please enter a valid Twitter URL',
        'invalid_linkedin_url' => 'Please enter a valid LinkedIn URL',
        'invalid_pinterest_url' => 'Please enter a valid Pinterest URL',
        'invalid_vk_url' => 'Please enter a valid VK URL',
        'invalid_telegram_url' => 'Please enter a valid Telegram URL',
        'required_t&c' => 'Terms & conditions field is required',
        'required_support' => 'Support field is required',
        'required_privacy' => 'Privacy field is required',
        'required_username' => 'Username field is required',
        'rss_feed_auto_update' => 'Auto Update RSS Feed',
        'select_time' => 'Select Time',
        'manual_payment_guide' => 'Manual Payment Guide',
        'download-db' => 'Download DB',
        'generate_sitemap' => 'Generate Sitemap',
        'run_commend' => 'Generate Sitemap',
        'social_media_sharing' => 'Social Media Shared',
        'facebook' => 'Facebook',
        'twitter' => 'Twitter',
        'linkedIn' => 'LinkedIn',
        'whatsapp' => 'Whatsapp',
        'reddit' => 'Reddit',
        'Stripe' => 'Stripe',
        'stripe_key' => 'Stripe Key',
        'stripe_secret_key' => 'Stripe Secret Key',
        'paypal_client_id' => 'Paypal Client ID',
        'paypal_secret' => 'Paypal Secret',
        'paypal_mode' => 'Paypal Mode',
        'open_ai_key' => 'Open AI Key',
        'Paypal' => 'Paypal',
        'Manually' => 'Manually',
        'advanced_setting' => 'Advanced Setting',
        'registration_system' => 'Registration System',
        'emoji_system' => 'Emoji System',
        'theme_configuration' => 'Theme configuration',
        'customer_main_image' => 'Section Main Image',
        'customer_main_image_placeholder' => 'Upload section main image',

        'section_one' => 'Section One',
        'section_two' => 'Section Two',
        'section_three' => 'Section Three',
        'section_four' => 'Section Four',

        'customer_experience_section' => 'Customer Experience Section',

        'customer_experience_image_1' => 'Section Image One',
        'customer_experience_image_1_placeholder' => 'Upload section image one',
        'customer_experience_title_1' => 'Section Title One',
        'customer_experience_title_1_placeholder' => 'Enter section title one',
        'customer_experience_description_1' => 'Section Description One',
        'customer_experience_description_1_placeholder' => 'Enter section description one',

        'customer_experience_image_2' => 'Section Image Two',
        'customer_experience_image_2_placeholder' => 'Upload section image two',
        'customer_experience_title_2' => 'Section Title Two',
        'customer_experience_title_2_placeholder' => 'Enter section title two',
        'customer_experience_description_2' => 'Section Description Two',
        'customer_experience_description_2_placeholder' => 'Enter section description two',

        'customer_experience_image_3' => 'Section Image Three',
        'customer_experience_image_3_placeholder' => 'Upload section image three',
        'customer_experience_title_3' => 'Section Title Three',
        'customer_experience_title_3_placeholder' => 'Enter section title three',
        'customer_experience_description_3' => 'Section Description Three',
        'customer_experience_description_3_placeholder' => 'Enter section description three',

        'customer_experience_image_4' => 'Section Image Four',
        'customer_experience_image_4_placeholder' => 'Upload section image four',
        'customer_experience_title_4' => 'Section Title Four',
        'customer_experience_title_4_placeholder' => 'Enter section title four',
        'customer_experience_description_4' => 'Section Description Four',
        'customer_experience_description_4_placeholder' => 'Enter section description four',

        'save' => 'Save',
        'customer_experience_updated_successfully' => 'Customer Experience Section Updated Successfully.',
    ],
    'payment_gateway' => [
        'payment_methods' => 'Payment Methods',
        'select_preferred_method' => 'Select your preferred payment method to complete your subscription',
        'no_plan_selected_title' => 'No Plan Selected',
        'no_plan_selected_body' => 'Please select a subscription plan first.',
        'back_to_plans' => '← Back',
        'continue_to_payment' => 'Pay',
        'confirm_payment_method' => 'Confirm Payment Method',
        'please_select_payment_method' => 'Please select a payment method.',
        'redirect_confirmation' => 'You will be redirected to :gatewayName to complete your payment for the :planName plan (:planPrice/:billingCycle).',
        'invalid_selection_title' => 'Invalid Selection',
        'invalid_selection_body' => 'Please select a payment method.',
        'plan_not_available_title' => 'Plan Not Available',
        'plan_not_available_body' => 'The selected plan is no longer available.',
        'gateway_not_available_title' => 'Payment Gateway Not Available',
        'gateway_not_available_body' => 'The selected payment method is currently unavailable.',
        'available_payment_methods' => 'Available Payment Methods',
        'no_gateways_available' => 'No payment gateways available for now.',
        'try_later' => 'Please try some after some time.',
        'secure_payment_processing' => 'Secure Payment Processing',
        'secure_payment_notice_1' => 'All payments are processed securely using industry-standard encryption.',
        'secure_payment_notice_2' => 'Your payment information is never stored on our servers.',
        'selected' => 'Selected',
        'agreement_notice_1' => 'By continuing, you agree to our',
        'terms_of_service' => 'Terms of Service',
        'agreement_notice_2' => 'and',
        'privacy_policy' => 'Privacy Policy',
        'agreement_notice_3' => 'You can cancel your subscription at any time.',
        'payment_pending_title' => 'Payment Pending',
        'payment_pending_body' => 'Please wait for the higher authority to approve your payment.',
        'stripe_name' => 'Stripe',
        'stripe_description' => 'Pay securely with your credit or debit card',
        'paypal_name' => 'PayPal',
        'paypal_description' => 'Pay with your PayPal account or credit card',
        'manual_name' => 'Manual',
        'manual_description' => 'Pay via Handover',
        'up_to_users' => 'Up to :count users',
        'unlimited_users' => 'Unlimited users',
        'up_to_customers' => 'Up to :count customers',
        'unlimited_customers' => 'Unlimited customers',
        'up_to_agents' => 'Up to :count agents',
        'unlimited_agents' => 'Unlimited agents',
        'user_activity_history' => 'Keep Track of Logged History',
    ],
    'contact' => [
        'contact' => 'Contact',
        'contact_information' => 'Contact Information',
        'name' => 'Name',
        'enter_your_name' => 'Enter your name',
        'email' => 'Email',
        'enter_your_email' => 'Enter your email address',
        'phone' => 'Phone',
        'subject' => 'Subject',
        'enter_subject' => 'Enter the subject of your message',
        'message' => 'Message',
        'enter_your_message' => 'Enter your message here',
        'contact_deleted_successfully' => 'Contact Deleted Successfully',
        'create_contact_title' => 'Create Contact',
        'contact_created_successfully' => 'Contact Created Successfully.',
        'list_contacts_title' => 'Contacts',
        'edit_contact_title' => 'Edit Contact',
        'contact_updated_successfully' => 'Contact Updated Successfully.',
    ],
    'mails' => [
        'mail' => 'Mail',
        'email' => 'E-Mail',
        'emailsend' => 'Send E-Mail',
        'mail_protocol' => 'Mail Protocol',
        'select_mail_protocol' => 'Select Mail Protocol',
        'select_encryption' => 'Select Encryption',
        'select_mail_library' => 'Select Mail Library',
        'encryption' => 'Encryption',
        'mail_library' => 'Mail Library',
        'mail_host' => 'Mail Host',
        'mail_port' => 'Mail Port',
        'mail_password' => 'Mail Password',
        'mail_title' => 'Mail Title',
        'reply_to' => 'Reply-To',
        'email_address' => 'Email Address',
        'mail_user_name' => 'Mail Username',
        'email_verification' => 'Email Verification',
        'contact_messages' => 'Contact Messages',
        'send_contact-messages_to_email_address' => 'Send Contact Messages to Email Address',
        'send_test_email' => 'Send Test Email',
        'thank_you_chose' => 'You have selected manual payment guide',
        'please_follow' => 'Please follow below step to paid manually.',
        'thanks_regard' => 'Thanks & Regards,',
        'new_manual_payment_request' => 'New Manual Payment Request',
        'verify_email' => 'Verify Email Address',
        'please_click' => 'Please click the button below to verify your email address.',
        'action_required' => 'If you did not create an account, no further action is required.',
        'hello' => 'Hello',
        'regard' => 'Regard',
        'manual_payment_status' => 'Manual Payment Status',
        'manual_payment_request' => 'Manual Payment Request',
        'test_mail' => 'Test Mail',
        'select_mail' => 'Please select Email',
        'you_can_not_send_more_than_5_mails' => 'You can not send more than 5 Mails',
        'mail_content' => 'Mail Content',
        'mail_subject' => 'Mail Subject',
        'reset_password_notification' => 'Reset Password Notification',
        'trouble' => "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser",
        'password_reset_request' => 'You are receiving this email because we received a password reset request for your account.',
        'this_password_reset_link_will_expire_in_count_minutes' => 'This password reset link will expire in :count minutes.',
        'no_further_action_is_required' => 'If you did not request a password reset, no further action is required.',
        'regards' => 'Regards,',
    ],
    'role' => [
        'role' => 'Role',
        'add_role' => 'Add Role',
        'edit_role' => 'Edit Role',
        'permissions' => 'Permissions',
        'role_permissions' => 'Role Permissions',
        'select_all_permissions' => 'Select All Permissions',
        'create_role_and_permissions' => 'Create Role and Permissions',
        'role_title' => 'Role Title',
        'enter_role_title' => 'Enter role title',
        'general_management' => 'General Management', // Default group name part
        'management_suffix' => 'Management', // Suffix for dynamic group names
        'cannot_delete_role' => 'Cannot delete this role!',
        'role_and_permissions_deleted_successfully' => 'Role & Permissions Deleted Successfully',
        'create_role_title' => 'Create Role',
        'role_already_exists' => 'Role already exists',
        'role_and_permissions_created_successfully' => 'Role & Permissions Created Successfully.',
        'list_roles_title' => 'Roles',
        'edit_role_title' => 'Edit Role',
        'role_and_permissions_updated_successfully' => 'Role & Permissions Updated Successfully.',
    ],
    'months' => [
        'jan' => 'Jan',
        'feb' => 'Feb',
        'mar' => 'Mar',
        'apr' => 'Apr',
        'may' => 'May',
        'jun' => 'Jun',
        'jul' => 'Jul',
        'aug' => 'Aug',
        'sep' => 'Sep',
        'oct' => 'Oct',
        'nov' => 'Nov',
        'dec' => 'Dec',
    ],
    'plans' => [
        'plan' => 'Plan',
        'plans' => 'Plans',
        'plan_name' => 'Plan Name',
        'select_currency' => 'Select Currency',
        'allowed_post' => 'Allowed Post',
        'save_price' => 'Save :price',
        'no_plans_available_title' => 'No Plans Available',
        'no_plans_available_body' => 'No :tab subscription plans are currently available.',
        'add_plan' => 'Add Plan',
        'enter_trial' => 'Enter Trial',
        'new_plan' => 'New Plan',
        'edit_plan' => 'Edit Plan',
        'frequency' => 'Frequency',
        'currency' => 'Currency',
        'price' => 'Price',
        'no_of_posts' => 'No Of Posts',
        'trial_days' => 'Trial Days',
        'monthly' => 'Monthly',
        'yearly' => 'Yearly',
        'unlimited' => 'Unlimited',
        'default_Plan' => 'Default Plan',
        'selected_plan' => 'Selected Plan',
        'unknown_plan' => 'Unknown Plan',
        'month' => 'month',
        'feature' => 'Feature',
        'plan_limits' => 'Plan Limits',
        'title' => 'Plan',
        'plan_title' => 'Title',
        'interval' => 'Interval',
        'amount' => 'Amount',
        'user_limit' => 'User Limit',
        'customer_limit' => 'Customer Limit',
        'agent_limit' => 'Agent Limit',
        'short_description' => 'Short Description',
        'is_show_history' => 'Show User Logged History',
        'most_popular' => 'Most Popular',
        'plan_deleted_successfully' => 'Plan Deleted Successfully',
        'plan_created_successfully' => 'Plan Created Successfully.',
        'plan_updated_successfully' => 'Plan Updated Successfully.',
        'default_popular_status_updated_successfully' => 'Default Popular Status Updated Successfully',
    ],
    'subscription' => [
        'choose_your_plan' => 'Choose Your Plan',
        'select_perfect_plan' => 'Select the perfect subscription plan for your needs',
        'monthly' => 'Monthly',
        'yearly' => 'Yearly',
        'month' => 'month',
        'year' => 'year',
        'need_help' => 'Need help choosing the right plan?',
        'contact_sales' => 'Contact our sales team',
        'subscription' => 'Subscription',
        'subscriptions' => 'Subscriptions',
        'transactions' => 'Transactions',
        'user' => 'User',
        'plan' => 'Plan',
        'amount' => 'Amount',
        'payment_method' => 'Payment Method',
        'payment_status' => 'Payment Status',
        'export' => 'Export',
        'active' => 'Active', // This might conflict with common.active, consider 'status_active' if so
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'subscription_updated_successfully' => 'Subscription Updated Successfully.',
        'subscription_deleted_successfully' => 'Subscription Deleted Successfully.',
        'manage_subscriptions_title' => 'Manage Subscriptions',
        'current_expire' => 'Current Expired Plan',
        'manage_subscription' => 'Manage Subscription',
        'cancel_subscription' => 'Cancel Subscription',
        'active_until' => 'Active Until',
        'active_until_unlimited' => 'Active till Unlimited',
        'expired' => 'Plan is Already Expired on',
        'upgrade_plan' => 'Upgrade Plan',
        'choose_plan' => 'Choose Plan',
        'no_plan_available' => 'No Plan Available',
        'purchase' => 'Purchase',
        'current_plan' => 'Current Plan',
        'trial_plan' => 'Trial Plan',
        'remaining' => 'Remaining',
        'subscribed_date' => 'Subscribed Date',
        'expired_date' => 'Expired date',
        'free' => 'Free',
        'history' => 'Subscription History',
        'plan_name' => 'Plan Name',
        'plan_price' => 'Plan Price',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'used_days' => 'Used Days',
        'remaining_days' => 'Remaining Days',
        'used_balance' => 'Used Balance',
        'remaining_balance' => 'Remaining Balance',
        'total_days' => 'Total Days',
        'payable_amount' => 'Payable Amount',
        'amount' => 'Amount',
        'currently_active' => 'Currently Active',
        'renew_plan' => 'Renew Plan',
        'renew_free_plan' => 'Free Plan cannot be renewed/chosen again',
        'proceed_to_payment' => 'Proceed to Payment',
        'switch_plan' => 'Switch Plan',
        'pay_or_switch_plan' => 'Pay / Switch Plan',
        'has_been_subscribed' => 'has been subscribed',
        'has_already_been_subscribed' => 'is already subscribed',
        'payment' => 'Payment',
        'days' => 'Days',
        'change_payment_status' => 'Change payment Status',
        'number_of_post' => 'Number Of Post',
        'duration' => 'Duration',
        'months' => 'Months',
        'what_in_startup_plan' => 'What`s In Startup Plan',
        'year' => 'Year',
        'select_payment' => 'Select Payment Type',
    ],
    'navigation' => [
        'dashboard' => 'Dashboard',
        'home' => 'Home',
        'business_management' => 'Business Management',
        'system_configuration' => 'System Configuration',
        'system_settings' => 'System Settings',
    ],
    'landing' => [
        'site' => [
            'tagline' => 'Modern Insurance Management Platform',
            'subtitle' => 'Streamline Operations',
        ],
        'navigation' => [
            'home' => 'Home',
            'features' => 'Features',
            'pricing' => 'Pricing',
            'contact' => 'Contact',
            'dashboard' => 'Dashboard',
            'start_free_trial' => 'Start Free Trial',
            'login' => 'Login',
        ],
        'languages' => [
            'arabic' => 'Arabic',
            'danish' => 'Danish',
            'dutch' => 'Dutch',
            'english' => 'English',
            'french' => 'French',
            'german' => 'German',
            'italian' => 'Italian',
            'japanese' => 'Japanese',
            'polish' => 'Polish',
            'portuguese' => 'Portuguese',
            'russian' => 'Russian',
            'spanish' => 'Spanish',
        ],
        'hero' => [
            'active_now' => 'Active Now',
            'no_setup_fees' => 'No setup fees',
            'free_trial' => '14-day free trial',
            'cancel_anytime' => 'Cancel anytime',
            'dashboard_alt' => 'Insurance Dashboard Screenshot',
            'cards' => [
                'policy_created' => 'Policy Created',
                'auto_insurance' => 'Auto Insurance Policy',
                'payment_processed' => 'Payment Processed',
                'premium_amount' => 'Premium $1,240/month',
                'active_agents' => 'Active Agents',
            ],
        ],
        'features' => [
            'title' => 'Platform Features',
            'subtitle' => 'How Insurance SaaS Works',
            'description' => 'Streamline your insurance operations with our comprehensive platform designed for modern insurance companies and agencies.',
        ],
        'customer_experience' => [
            'title' => 'Customer Experience',
            'heading' => 'Empower Your Customers',
            'description' => 'Provide your customers with a beautiful, intuitive dashboard where they can access all their insurance information in one place.',
            'dashboard_alt' => 'Customer Dashboard Screenshot',
            'cards' => [
                'payment_due' => 'Payment Due',
                'next_installment' => 'Next installment due in 5 days',
                'auto_policy' => 'Auto Policy Active',
                'coverage' => 'Full coverage until 2025',
            ],
        ],
        'why_choose' => [
            'title' => 'Why Choose Us',
            'heading' => 'Built for Modern Insurance',
            'description' => 'Designed specifically for insurance companies who want to scale operations and empower agency partners with cutting-edge technology.',
        ],
        'testimonials' => [
            'title' => 'Client Success Stories',
            'heading' => 'Trusted by Industry Leaders',
            'description' => 'Join thousands of insurance professionals who\'ve transformed their operations with our platform',
        ],
        'pricing' => [
            'title' => 'Simple Pricing',
            'heading' => 'Choose Your Perfect Plan',
            'description' => 'Scale your insurance operations with transparent pricing. No hidden fees, no surprises.',
            'most_popular' => 'MOST POPULAR',
            'month' => 'month',
            'year' => 'year',
            'start_free' => 'Start Free',
            'start_trial' => 'Start Free Trial',
            'free' => 'Free',
            'default_description' => 'A great option for your needs',
            'trial_info' => 'All plans include a 14-day free trial. No credit card required.',
            'features' => [
                'users' => 'Create :count Users',
                'customers' => 'Create :count Customers',
                'agents' => 'Create :count Agents',
                'logged_history' => 'Keep Track of Logged History',
            ],
            'compliance' => [
                'soc2' => 'SOC 2 Compliant',
                'security' => 'Bank-Level Security',
                'support' => 'Expert Support',
                'uptime' => '99.9% Uptime SLA',
            ],
        ],
        'faq' => [
            'title' => 'Frequently Asked Questions',
            'heading' => 'Got Questions? We\'ve Got Answers',
            'description' => 'Everything you need to know about Insurance SaaS',
        ],
        'cta' => [
            'heading' => 'Ready to Transform Your Insurance Business?',
            'description' => 'Join hundreds of insurance companies already using Insurance SaaS to scale their operations and delight their customers.',
            'button' => 'Start Your Free Trial',
            'features' => [
                'no_credit_card' => 'No credit card required',
                'quick_setup' => 'Setup in under 15 minutes',
                'security' => 'Enterprise-grade security',
            ],
        ],
        'footer' => [
            'rights' => 'All rights reserved.',
            'privacy_policy' => 'Privacy Policy',
            'terms_conditions' => 'Terms & Conditions',
        ],
    ],
    'hero_section' => [
        'title' => 'Hero Section',
        'tag_line' => 'Tag Line',
        'hero_section_title' => 'Title',
        'hero_section_short_description' => 'Short Description',
        'hero_section_image' => 'Hero Section Image',
        'save' => 'Save',
        'updated_successfully' => 'Hero Section Updated Successfully.',
    ],
    'feature_section' => [
        'title' => 'Feature Section',
        'feature_one' => 'Feature One',
        'feature_two' => 'Feature Two',
        'feature_three' => 'Feature Three',
        'feature_four' => 'Feature Four',
        'feature_five' => 'Feature Five',
        'feature_six' => 'Feature Six',
        'feature_title_1' => 'Feature Title One',
        'feature_description_1' => 'Feature Description One',
        'feature_image_1' => 'Feature Image One',
        'feature_title_2' => 'Feature Title Two',
        'feature_description_2' => 'Feature Description Two',
        'feature_image_2' => 'Feature Image Two',
        'feature_title_3' => 'Feature Title Three',
        'feature_description_3' => 'Feature Description Three',
        'feature_image_3' => 'Feature Image Three',
        'feature_title_4' => 'Feature Title Four',
        'feature_description_4' => 'Feature Description Four',
        'feature_image_4' => 'Feature Image Four',
        'feature_title_5' => 'Feature Title Five',
        'feature_description_5' => 'Feature Description Five',
        'feature_image_5' => 'Feature Image Five',
        'feature_title_6' => 'Feature Title Six',
        'feature_description_6' => 'Feature Description Six',
        'feature_image_6' => 'Feature Image Six',
        'save' => 'Save',
        'updated_successfully' => 'Feature Section Updated Successfully.',
    ],
    'why_choose_us' => [
        'title' => 'Why Choose Us',
        'why_choose_us_image_1' => 'Section Image One',
        'why_choose_us_title_1' => 'Section Title One',
        'why_choose_us_description_1' => 'Section Description One',

        'why_choose_us_image_2' => 'Section Image Two',
        'why_choose_us_title_2' => 'Section Title Two',
        'why_choose_us_description_2' => 'Section Description Two',

        'why_choose_us_image_3' => 'Section Image Three',
        'why_choose_us_title_3' => 'Section Title Three',
        'why_choose_us_description_3' => 'Section Description Three',

        'why_choose_us_image_4' => 'Section Image Four',
        'why_choose_us_title_4' => 'Section Title Four',
        'why_choose_us_description_4' => 'Section Description Four',

        'why_choose_us_updated_successfully' => 'Why Choose Us Section Updated Successfully',
    ],
    'testimonial' => [
        'model_label' => 'Testimonials',
        'name' => 'Name',
        'title' => 'Title',
        'image' => 'Image',
        'description' => 'Description',
        'actions' => 'Actions',
        'add_testimonial' => 'Add Testimonial',
        'created_successfully' => 'Testimonial created successfully',
        'updated_successfully' => 'Testimonial updated successfully',
        'deleted_successfully' => 'Testimonial deleted successfully',
    ],
    'faq' => [
        'model_label' => 'FAQs',
        'question' => 'Question',
        'answer' => 'Answer',
        'actions' => 'Actions',
        'add_faq' => 'Add FAQ',
        'created_successfully' => 'FAQ created successfully',
        'updated_successfully' => 'FAQ updated successfully',
        'deleted_successfully' => 'FAQ deleted successfully',
    ],
    'footer_section' => [
        'navigation_label' => 'Footer Section',
        'title' => 'Footer Section',
        'footer_links' => 'Footer Links',
        'twitter_link' => 'Twitter Link',
        'twitter_placeholder' => 'https://twitter.com/',
        'linkedin_link' => 'Linkedin Link',
        'linkedin_placeholder' => 'https://linkedin.com/',
        'facebook_link' => 'Facebook Link',
        'facebook_placeholder' => 'https://facebook.com/',
        'privacy_terms_section' => 'Privacy Policy | Terms and Conditions',
        'privacy_policy' => 'Privacy Policy',
        'terms_and_conditions' => 'Terms and Conditions',
        'save' => 'Save',
        'updated_successfully' => 'Footer Section Updated Successfully.',
    ],
    'super_admin_setting' => [
        'navigation_label' => 'General',
        'title' => 'General',
        'app_name' => 'App Name',
        'app_name_placeholder' => 'Enter App Name',
        'logo' => 'Logo',
        'favicon' => 'Favicon',
        'light_logo' => 'Light Logo',
        'landing_logo' => 'Landing Logo',
        'auth_bg_image' => 'Authentication Background Image',
        'enable_email_verification' => 'Enable Email Verification',
        'enable_registration_page' => 'Enable Registration Page',
        'enable_landing_page' => 'Enable Landing Page',
        'save' => 'Save',
        'updated_successfully' => 'Settings Updated Successfully.',
    ],
    'payment_setting' => [
        'navigation_label' => 'Payment',
        'title' => 'Payment',
        'manual_payment' => 'Manual Payment',
        'enable_manual' => 'Enable Manual',
        'stripe_payment' => 'Stripe Payment',
        'enable_stripe' => 'Enable Stripe',
        'stripe_key' => 'Stripe Key',
        'stripe_secret' => 'Stripe Secret',
        'paypal_payment' => 'Paypal Payment',
        'enable_paypal' => 'Enable PayPal',
        'paypal_mode' => 'PayPal Mode',
        'paypal_client' => 'PayPal Client',
        'paypal_secret' => 'PayPal Secret',
        'enabled' => 'Enabled',
        'disabled' => 'Disabled',
        'sandbox' => 'Sandbox',
        'live' => 'Live',
        'save' => 'Save',
        'please_add_paypal_client' => 'Please add PayPal Client',
        'please_add_paypal_secret' => 'Please add PayPal Secret',
        'please_add_stripe_key' => 'Please add Stripe Key',
        'please_add_stripe_secret' => 'Please add Stripe Secret',
        'updated_successfully' => 'Payment Settings Updated Successfully.',
    ], 'google_recaptcha' => [
        'navigation_label' => 'Google ReCaptcha',
        'title' => 'Google ReCaptcha',
        'enable' => 'Enable Google ReCaptcha',
        'enabled' => 'Enabled',
        'disabled' => 'Disabled',
        'site_key' => 'Google ReCaptcha Site Key',
        'secret_key' => 'Google ReCaptcha Secret Key',
        'updated_successfully' => 'Google ReCaptcha Updated Successfully.',
    ],
    'seo_settings' => [
        'title' => 'SEO Settings',
        'meta_title' => 'Enter Meta Title',
        'meta_title_placeholder' => 'Meta Title',

        'meta_keyword' => 'Enter Meta Keyword',
        'meta_keyword_placeholder' => 'Meta Keyword',

        'meta_description' => 'Enter Meta Description',
        'meta_description_placeholder' => 'Meta Description',

        'meta_image' => 'Meta Image',

        'updated_successfully' => 'SEO Settings Updated Successfully.',
    ],

];
