{"Activate": "Activate", "Activate Two-Factor Authentication": "Activate Two-Factor Authentication", "Add additional security to your account using two factor authentication": "Add additional security to your account using two factor authentication", "Authenticate with your code": "Authenticate with your code", "Authenticator app": "Authenticator app", "Cancel": "Cancel", "Code": "Code", "Confirm": "Confirm", "Deactivate": "Deactivate", "Email": "Email", "From now on, you will be asked for a code when you log in.": "From now on, you will be asked for a code when you log in.", "Hello": "Hello", "If you didn't try to log in, please change your password immediately to protect your account.": "If you didn't try to log in, please change your password immediately to protect your account.", "Kind regards": "Kind regards", "Login": "<PERSON><PERSON>", "Or scan the QR code with your authenticator app": "Or scan the QR code with your authenticator app", "Password Confirmation": "Password Confirmation", "Phone number": "Phone number", "email": "email", "phone": "phone", "authenticator app": "authenticator app", "Please confirm this is your :type before continuing.": "Please confirm this is your :type before continuing.", "Recovery code": "Recovery code", "Regenerate": "Regenerate", "Resend": "Resend", "Reset Password": "Reset Password", "SMS": "SMS", "Save these recovery codes in a secure place as they can be used to recover access to your account if you lose your device": "Save these recovery codes in a secure place as they can be used to recover access to your account if you lose your device", "Secure your account": "Secure your account", "Submit": "Submit", "Successfully resend the OTP code": "Successfully resend the OTP code", "The provided two factor authentication code was invalid.": "The provided two factor authentication code was invalid.", "The secret key to setup the authenticator app is": "The secret key to setup the authenticator app is", "Two-Factor Authentication": "2FA Settings", "Two-Factor Authentication activated": "Two-Factor Authentication activated", "Two-Factor Authentication deactivated": "Two-Factor Authentication deactivated", "Two-Factor Authentication enabled": "Two-Factor Authentication enabled", "Verify": "Verify", "You can disable two factor authentication at any time by using the button below": "You can disable two factor authentication at any time by using the button below", "You can now log in without a code.": "You can now log in without a code.", "You have :amount options to confirm your identity, please choose one of the options below to continue": "You have :amount options to confirm your identity, please choose one of the options below to continue", "You recently requested to log in to your account. To complete the login, please use the following two-factor authentication (2FA) code:": "You recently requested to log in to your account. To complete the login, please use the following two-factor authentication (2FA) code:", "Your account has been secured with two factor authentication": "Your account has been secured with two factor authentication", "Your security code for :app": "Your security code for :app", "Your security code is": "Your security code is"}