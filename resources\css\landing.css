@import '@fortawesome/fontawesome-free/css/all.min.css';

* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* PolicyBazaar-style Variables */
:root {
    --pb-primary: #1e40af;
    --pb-primary-dark: #1e3a8a;
    --pb-secondary: #f97316;
    --pb-secondary-dark: #ea580c;
    --pb-success: #10b981;
    --pb-danger: #ef4444;
    --pb-warning: #f59e0b;
    --pb-info: #3b82f6;
    --pb-light: #f8fafc;
    --pb-dark: #1f2937;
    --pb-gray: #6b7280;
}

/* PolicyBazaar-style Components */
.pb-btn-primary {
    background: linear-gradient(135deg, var(--pb-primary) 0%, var(--pb-primary-dark) 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.pb-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
}

.pb-btn-secondary {
    background: linear-gradient(135deg, var(--pb-secondary) 0%, var(--pb-secondary-dark) 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.pb-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(249, 115, 22, 0.3);
}

/* Insurance Card Styles */
.insurance-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.insurance-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--pb-primary);
}

.insurance-type-btn {
    transition: all 0.2s ease;
}

.insurance-type-btn:hover {
    border-color: var(--pb-primary);
    background-color: rgba(30, 64, 175, 0.05);
}

.insurance-type-btn.active {
    border-color: var(--pb-primary);
    background-color: rgba(30, 64, 175, 0.1);
}

/* Navigation Styles */
.nav-dropdown {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.nav-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Mobile Menu Styles */
.mobile-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.mobile-menu.show {
    max-height: 500px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes pulse-blue {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(30, 64, 175, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(30, 64, 175, 0);
    }
}

.pulse-blue {
    animation: pulse-blue 2s infinite;
}

.hero-gradient {
    background: linear-gradient(135deg, #0c4a6e 0%, #0369a1 25%, #0284c7 50%, #0ea5e9 75%, #38bdf8 100%);
    position: relative;
    overflow: hidden;
}

.hero-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(6, 182, 212, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.15) 0%, transparent 50%);
}

.floating-image {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    25% {
        transform: translateY(-10px) rotate(1deg);
    }

    50% {
        transform: translateY(-5px) rotate(-1deg);
    }

    75% {
        transform: translateY(-15px) rotate(0.5deg);
    }
}

.glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(79, 70, 229, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.mobile-menu {
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.active {
    transform: translateX(0);
}

@media (max-width: 640px) {
    .hero-gradient {
        min-height: 100vh;
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .floating-image {
        animation: none;
        transform: none !important;
    }

    .feature-card {
        margin-bottom: 1rem;
    }

    .testimonial-slider-mobile {
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .testimonial-slider-mobile::-webkit-scrollbar {
        display: none;
    }

    .testimonial-slide {
        scroll-snap-align: start;
        flex-shrink: 0;
    }
}

html {
    scroll-behavior: smooth;
}

.smooth-scroll {
    scroll-behavior: smooth;
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-divider {
    background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
    height: 1px;
}

.btn-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.4);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
