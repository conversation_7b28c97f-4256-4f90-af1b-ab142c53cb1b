
// PolicyBazaar-style JavaScript functionality

// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Mobile dropdown toggles
        const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
        mobileDropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const content = toggle.nextElementSibling;
                const icon = toggle.querySelector('i');

                content.classList.toggle('hidden');
                icon.classList.toggle('rotate-180');
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    }

    // Insurance type selector
    const insuranceTypeBtns = document.querySelectorAll('.insurance-type-btn');
    insuranceTypeBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons
            insuranceTypeBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            btn.classList.add('active');
        });
    });

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Form validation and submission
    const quoteForm = document.querySelector('form');
    if (quoteForm) {
        quoteForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Basic form validation
            const inputs = quoteForm.querySelectorAll('input[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('border-red-500');
                } else {
                    input.classList.remove('border-red-500');
                }
            });

            if (isValid) {
                // Show success message or redirect
                alert('Thank you! We will contact you shortly with your insurance quote.');
            } else {
                alert('Please fill in all required fields.');
            }
        });
    }

    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);

    // Observe insurance cards and other elements
    const elementsToAnimate = document.querySelectorAll('.insurance-card, .bg-white');
    elementsToAnimate.forEach(el => observer.observe(el));
});

// FAQ toggle functionality
const faqQuestions = document.querySelectorAll('.faq-question');

faqQuestions.forEach(question => {
    question.addEventListener('click', () => {
        const targetId = question.getAttribute('data-target');
        const answer = document.getElementById(targetId);
        const icon = question.querySelector('i');

        // Close other FAQ items
        faqQuestions.forEach(otherQuestion => {
            if (otherQuestion !== question) {
                const otherTargetId = otherQuestion.getAttribute('data-target');
                const otherAnswer = document.getElementById(otherTargetId);
                const otherIcon = otherQuestion.querySelector('i');

                if (otherAnswer) otherAnswer.classList.add('hidden');
                if (otherIcon) otherIcon.style.transform = 'rotate(0deg)';
            }
        });

        // Toggle current FAQ item
        if (answer && icon) {
            if (answer.classList.contains('hidden')) {
                answer.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                answer.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    });
});

// Testimonial Slider Functionality
let currentSlide = 0;
const testimonialTrack = document.getElementById('testimonial-track');
const testimonialDots = document.querySelectorAll('#testimonial-dots button');
const prevButton = document.getElementById('prev-testimonial');
const nextButton = document.getElementById('next-testimonial');

if (testimonialTrack && testimonialDots.length > 0) {
    const totalSlides = testimonialDots.length;

    function updateSlider() {
        const translateX = -currentSlide * 100;
        testimonialTrack.style.transform = `translateX(${translateX}%)`;

        // Update dots
        testimonialDots.forEach((dot, index) => {
            if (index === currentSlide) {
                dot.className = 'w-2 md:w-3 h-2 md:h-3 rounded-full bg-indigo-600 transition-all duration-300';
            } else {
                dot.className = 'w-2 md:w-3 h-2 md:h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300';
            }
        });
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlider();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateSlider();
    }

    function goToSlide(slideIndex) {
        currentSlide = slideIndex;
        updateSlider();
    }

    // Event listeners
    if (nextButton) nextButton.addEventListener('click', nextSlide);
    if (prevButton) prevButton.addEventListener('click', prevSlide);

    testimonialDots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
    });

    // Auto-play slider
    setInterval(nextSlide, 5000);
}

// Language dropdown functionality
const languageDropdown = document.getElementById('language-dropdown');
const languageMenu = document.getElementById('language-menu');

if (languageDropdown && languageMenu) {
    languageDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
        languageMenu.classList.toggle('hidden');
    });

    // Close language dropdown when clicking outside
    document.addEventListener('click', () => {
        languageMenu.classList.add('hidden');
    });

    // Language selection
    languageMenu.addEventListener('click', (e) => {
        if (e.target.closest('a')) {
            const flag = e.target.closest('a').querySelector('span:first-child').textContent;
            const lang = e.target.closest('a').querySelector('span:last-child').textContent;
            const langCode = lang === 'English' ? 'EN' : lang === 'Español' ? 'ES' : lang === 'Français' ? 'FR' : 'DE';

            languageDropdown.querySelector('span:first-child').textContent = flag;
            languageDropdown.querySelector('span:nth-child(2)').textContent = langCode;
            languageMenu.classList.add('hidden');
        }
    });
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
