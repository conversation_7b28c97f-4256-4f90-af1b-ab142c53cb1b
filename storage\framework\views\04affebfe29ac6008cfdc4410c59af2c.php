<?php $__env->startSection('content'); ?>
    <!-- PolicyBazaar-style Hero Section -->
    <section class="bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 min-h-screen relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-32 h-32 bg-white rounded-full animate-pulse"></div>
            <div class="absolute bottom-20 right-20 w-24 h-24 bg-white rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 right-1/4 w-16 h-16 bg-white rounded-full animate-pulse" style="animation-delay: 2s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
                <!-- Left Content -->
                <div class="text-left">
                    <!-- Trust Badge -->
                    <div class="inline-flex items-center bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                        <span class="text-white/90 font-medium">Trusted by <?php echo e(number_format($insuranceStats['total_customers'])); ?>+ customers</span>
                    </div>

                    <!-- Main Heading -->
                    <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        India's Largest
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                            Insurance
                        </span>
                        Marketplace
                    </h1>

                    <!-- Subheading -->
                    <p class="text-xl text-white/80 mb-8 leading-relaxed">
                        Compare & buy insurance plans from <?php echo e($insuranceStats['partner_companies']); ?>+ top insurers.
                        Get the best deals on Health, Life, Car & Bike insurance.
                    </p>

                    <!-- Stats -->
                    <div class="grid grid-cols-2 gap-6 mb-8">
                        <div class="text-center lg:text-left">
                            <div class="text-3xl font-bold text-white"><?php echo e(number_format($insuranceStats['total_policies'])); ?>+</div>
                            <div class="text-white/70">Policies Sold</div>
                        </div>
                        <div class="text-center lg:text-left">
                            <div class="text-3xl font-bold text-white"><?php echo e(number_format($insuranceStats['total_claims_processed'])); ?>+</div>
                            <div class="text-white/70">Claims Processed</div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <button onclick="document.getElementById('insurance-search').scrollIntoView({behavior: 'smooth'})"
                            class="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-search mr-2"></i>
                            Find Best Insurance
                        </button>
                        <button onclick="window.location.href='<?php echo e(route('auth')); ?>'"
                            class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/20 transition-all duration-300">
                            <i class="fas fa-calculator mr-2"></i>
                            Calculate Premium
                        </button>
                    </div>
                </div>

                <!-- Right Content - Insurance Search Card -->
                <div class="order-1 lg:order-2">
                    <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-md mx-auto">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Get Insurance Quote</h3>

                        <!-- Insurance Type Selector -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Select Insurance Type</label>
                            <div class="grid grid-cols-2 gap-3">
                                <?php $__currentLoopData = array_slice($insuranceCategories, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button class="insurance-type-btn p-3 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-center group">
                                    <i class="<?php echo e($category['icon']); ?> text-2xl text-gray-400 group-hover:text-blue-500 mb-2"></i>
                                    <div class="text-sm font-medium text-gray-700 group-hover:text-blue-600"><?php echo e($category['name']); ?></div>
                                </button>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Quick Form -->
                        <form class="space-y-4">
                            <div>
                                <input type="text" placeholder="Your Name"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <input type="tel" placeholder="Mobile Number"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>Select Age Group</option>
                                    <option>18-25 years</option>
                                    <option>26-35 years</option>
                                    <option>36-45 years</option>
                                    <option>46-55 years</option>
                                    <option>55+ years</option>
                                </select>
                            </div>
                            <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg">
                                Get Free Quote
                            </button>
                        </form>

                        <!-- Trust Indicators -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-center space-x-4 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-green-500 mr-1"></i>
                                    100% Secure
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-blue-500 mr-1"></i>
                                    Free Consultation
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Insurance Products Section -->
    <section id="insurance-search" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Choose Your Insurance
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Compare and buy the best insurance plans from top insurers. Get instant quotes and expert advice.
                </p>
            </div>

            <!-- Insurance Categories Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <?php $__currentLoopData = $insuranceCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group cursor-pointer">
                    <div class="p-8">
                        <!-- Icon and Popular Badge -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="w-16 h-16 <?php echo e($category['color']); ?> rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="<?php echo e($category['icon']); ?> text-2xl text-white"></i>
                            </div>
                            <?php if($category['popular']): ?>
                            <span class="bg-orange-100 text-orange-600 text-xs font-semibold px-3 py-1 rounded-full">
                                Popular
                            </span>
                            <?php endif; ?>
                        </div>

                        <!-- Content -->
                        <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($category['name']); ?></h3>
                        <p class="text-gray-600 mb-4 text-sm leading-relaxed"><?php echo e($category['description']); ?></p>

                        <!-- Pricing -->
                        <div class="mb-6">
                            <span class="text-lg font-bold text-blue-600"><?php echo e($category['starting_price']); ?></span>
                        </div>

                        <!-- Features -->
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Instant Policy Issuance
                            </li>
                            <li class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                24/7 Claim Support
                            </li>
                            <li class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                No Medical Checkup*
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Popular Policies Section -->
            <?php if($popularPolicies->isNotEmpty()): ?>
            <div class="mt-16">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Popular Insurance Policies</h3>
                    <p class="text-gray-600">Most chosen policies by our customers</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $popularPolicies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start justify-between mb-4">
                            <div>
                                <h4 class="font-semibold text-gray-900"><?php echo e($policy->title); ?></h4>
                                <p class="text-sm text-gray-600"><?php echo e($policy->policyType->name ?? 'General'); ?></p>
                            </div>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">
                                <?php echo e(ucfirst($policy->coverage_type == 1 ? 'Family' : ($policy->coverage_type == 2 ? 'Group' : 'Individual'))); ?>

                            </span>
                        </div>

                        <div class="mb-4">
                            <div class="text-lg font-bold text-blue-600">
                                ₹<?php echo e(number_format($policy->policyPricings->min('price') ?? 0)); ?>

                                <span class="text-sm font-normal text-gray-500">/year</span>
                            </div>
                            <div class="text-sm text-gray-600">Sum Assured: ₹<?php echo e(number_format($policy->sum_assured)); ?></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-users mr-1"></i>
                                <?php echo e($policy->total_insured_person); ?> Person<?php echo e($policy->total_insured_person > 1 ? 's' : ''); ?>

                            </div>
                            <button class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                View Details →
                            </button>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Why Choose <?php echo e(appName()); ?>?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    We make insurance simple, transparent, and accessible for everyone
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">100% Secure</h3>
                    <p class="text-gray-600 text-sm">Your data is protected with bank-level security</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Quick Process</h3>
                    <p class="text-gray-600 text-sm">Get your policy in just 5 minutes</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-headset text-2xl text-orange-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">24/7 Support</h3>
                    <p class="text-gray-600 text-sm">Expert support whenever you need it</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-award text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Best Prices</h3>
                    <p class="text-gray-600 text-sm">Compare and get the best deals</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Customer Testimonials Section -->
    <?php if($testimonialSection->isNotEmpty()): ?>
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    What Our Customers Say
                </h2>
                <p class="text-xl text-gray-600">
                    Join thousands of satisfied customers who trust us with their insurance needs
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $testimonialSection; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <!-- Rating Stars -->
                    <div class="flex items-center mb-4">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star text-yellow-400"></i>
                        <?php endfor; ?>
                        <span class="ml-2 text-sm text-gray-600">5.0</span>
                    </div>

                    <!-- Testimonial Text -->
                    <p class="text-gray-700 mb-4 leading-relaxed">
                        "<?php echo e($testimonial->message); ?>"
                    </p>

                    <!-- Customer Info -->
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-semibold"><?php echo e(substr($testimonial->name, 0, 1)); ?></span>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900"><?php echo e($testimonial->name); ?></div>
                            <div class="text-sm text-gray-600"><?php echo e($testimonial->designation ?? 'Verified Customer'); ?></div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Insurance Partners Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Our Insurance Partners
                </h2>
                <p class="text-xl text-gray-600">
                    Leading insurers for your financial security
                </p>
            </div>

            <!-- Partner Logos Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
                <!-- Life Insurance Partners -->
                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-shield-alt text-2xl text-blue-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">LIC</div>
                    </div>
                </div>

                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-heart text-2xl text-red-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">HDFC Life</div>
                    </div>
                </div>

                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-leaf text-2xl text-green-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">SBI Life</div>
                    </div>
                </div>

                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-star text-2xl text-purple-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">ICICI Prudential</div>
                    </div>
                </div>

                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-sun text-2xl text-orange-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">Bajaj Allianz</div>
                    </div>
                </div>

                <div class="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-gem text-2xl text-indigo-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">Max Life</div>
                    </div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?php echo e($insuranceStats['partner_companies']); ?>+</div>
                    <div class="text-gray-600">Insurance Partners</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-600 mb-2"><?php echo e(number_format($insuranceStats['total_customers'])); ?>+</div>
                    <div class="text-gray-600">Happy Customers</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-orange-600 mb-2"><?php echo e(number_format($insuranceStats['total_policies'])); ?>+</div>
                    <div class="text-gray-600">Policies Sold</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Insurance Calculator Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Insurance Calculators & Tools
                </h2>
                <p class="text-xl text-gray-600">
                    Calculate your insurance needs with our smart tools
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Premium Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calculator text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Premium Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Calculate your insurance premium based on your profile and coverage needs</p>
                    <button class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-300">
                        Calculate Premium
                    </button>
                </div>

                <!-- Coverage Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Coverage Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Determine the right amount of coverage for your family's financial security</p>
                    <button class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-300">
                        Calculate Coverage
                    </button>
                </div>

                <!-- EMI Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-credit-card text-2xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">EMI Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Calculate your monthly EMI for insurance premium payments</p>
                    <button class="w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-300">
                        Calculate EMI
                    </button>
                </div>

                <!-- Tax Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-percentage text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Tax Savings Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Calculate tax savings on your insurance premium payments</p>
                    <button class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-300">
                        Calculate Tax Savings
                    </button>
                </div>

                <!-- Maturity Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-line text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Maturity Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Calculate maturity amount for your investment-linked insurance plans</p>
                    <button class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-300">
                        Calculate Maturity
                    </button>
                </div>

                <!-- Claim Calculator -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-medical text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Claim Calculator</h3>
                    <p class="text-gray-600 text-center mb-6">Estimate your claim amount and settlement process timeline</p>
                    <button class="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors duration-300">
                        Calculate Claim
                    </button>
                </div>
            </div>

            <!-- Additional Tools -->
            <div class="mt-12 text-center">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">More Insurance Tools</h3>
                <div class="flex flex-wrap justify-center gap-4">
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                        <i class="fas fa-search mr-2"></i>
                        Policy Comparison
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Renewal Reminder
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                        <i class="fas fa-mobile-alt mr-2"></i>
                        Mobile App
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                        <i class="fas fa-headset mr-2"></i>
                        Expert Consultation
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 to-blue-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to Get Insured?
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join millions of Indians who trust us for their insurance needs. Get started today!
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                    <i class="fas fa-search mr-2"></i>
                    Compare Insurance Plans
                </button>
                <button class="bg-orange-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-orange-600 transition-colors duration-300">
                    <i class="fas fa-phone mr-2"></i>
                    Talk to Expert
                </button>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\IIB\resources\views/home/<USER>/ ?>