<!-- PolicyBazaar-style Navigation -->
<nav class="bg-white shadow-lg sticky top-0 z-50">
    <!-- Top Bar with Contact Info -->
    <div class="bg-blue-600 text-white py-2">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center text-sm">
                <div class="flex items-center space-x-6">
                    <div class="flex items-center">
                        <i class="fas fa-phone mr-2"></i>
                        <span>1800-208-8787</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-2"></i>
                        <span>care{{ strtolower(appName()) }}.com</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#" class="hover:text-blue-200 transition-colors">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" class="hover:text-blue-200 transition-colors">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="hover:text-blue-200 transition-colors">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <img src="<?php echo e(appLandingLogo()); ?>" alt="<?php echo e(appName()); ?>" class="h-10 w-auto">
                <span class="ml-3 text-xl font-bold text-gray-900"><?php echo e(appName()); ?></span>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-8">
                <!-- Insurance Products Dropdown -->
                <div class="relative group">
                    <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
                        Insurance Products
                        <i class="fas fa-chevron-down ml-1 text-xs group-hover:rotate-180 transition-transform duration-200"></i>
                    </button>
                    <div class="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 border border-gray-100">
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4">
                                <?php if(isset($insuranceCategories)): ?>
                                    <?php $__currentLoopData = $insuranceCategories->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="#" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                                        <div class="w-8 h-8 <?php echo e($category['color']); ?> rounded-lg flex items-center justify-center mr-3">
                                            <i class="<?php echo e($category['icon']); ?> text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900 text-sm"><?php echo e($category['name']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo e($category['starting_price']); ?></div>
                                        </div>
                                    </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Renew Policy -->
                <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
                    Renew Policy
                </a>

                <!-- Claim -->
                <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
                    Claim
                </a>

                <!-- Support -->
                <div class="relative group">
                    <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
                        Support
                        <i class="fas fa-chevron-down ml-1 text-xs group-hover:rotate-180 transition-transform duration-200"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 border border-gray-100">
                        <div class="p-4">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                <i class="fas fa-phone mr-2"></i>Call Support
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                <i class="fas fa-comments mr-2"></i>Live Chat
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                <i class="fas fa-envelope mr-2"></i>Email Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side Elements -->
            <div class="hidden lg:flex items-center space-x-4">
                <!-- My Account Dropdown -->
                <div class="relative group">
                    <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-user mr-2"></i>
                        My Account
                        <i class="fas fa-chevron-down ml-1 text-xs group-hover:rotate-180 transition-transform duration-200"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 border border-gray-100">
                        <div class="p-2">
                            <?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('auth')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-file-alt mr-2"></i>My Policies
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-credit-card mr-2"></i>Payments
                                </a>
                                <hr class="my-2">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                                </a>
                            <?php else: ?>
                                <a href="<?php echo e(route('filament.admin.auth.login')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                                </a>
                                <a href="<?php echo e(route('filament.admin.auth.register')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-lg">
                                    <i class="fas fa-user-plus mr-2"></i>Register
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Language Switcher -->
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('language-switcher');

$__html = app('livewire')->mount($__name, $__params, 'lw-3182161347-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button type="button" class="mobile-menu-button text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 p-2">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div class="mobile-menu hidden lg:hidden bg-white border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
            <!-- Insurance Products -->
            <div>
                <button class="mobile-dropdown-toggle flex items-center justify-between w-full text-left text-gray-700 font-medium py-2">
                    Insurance Products
                    <i class="fas fa-chevron-down text-sm"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 pt-2 space-y-2">
                    <?php if(isset($insuranceCategories)): ?>
                        <?php $__currentLoopData = $insuranceCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="#" class="block py-2 text-sm text-gray-600 hover:text-blue-600">
                            <i class="<?php echo e($category['icon']); ?> mr-2"></i><?php echo e($category['name']); ?>

                        </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <a href="#" class="block text-gray-700 font-medium py-2 hover:text-blue-600">Renew Policy</a>
            <a href="#" class="block text-gray-700 font-medium py-2 hover:text-blue-600">Claim</a>
            <a href="#" class="block text-gray-700 font-medium py-2 hover:text-blue-600">Support</a>

            <div class="pt-4 border-t border-gray-200">
                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('auth')); ?>" class="block text-gray-700 font-medium py-2 hover:text-blue-600">Dashboard</a>
                <?php else: ?>
                    <a href="<?php echo e(route('filament.admin.auth.login')); ?>" class="block text-gray-700 font-medium py-2 hover:text-blue-600">Sign In</a>
                    <a href="<?php echo e(route('filament.admin.auth.register')); ?>" class="block bg-blue-600 text-white text-center py-3 rounded-lg font-medium mt-2">Register</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>
<?php /**PATH C:\Users\<USER>\Desktop\IIB\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>