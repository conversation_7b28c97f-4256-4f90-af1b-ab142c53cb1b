<div class="relative ml-2" x-data="{ open: false }">
    <button @click="open = !open"
        class="flex items-center text-gray-700 hover:text-sky-600 hover:bg-sky-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-300">
        <span class="mr-2 text-2xl"><?php echo e($this->languageLabel['flag']); ?></span>
        <span class="mr-1"><?php echo e(__($this->languageLabel['label'])); ?></span>
        <i class="fas fa-chevron-down ml-1 text-xs"></i>
    </button>

    <div x-show="open" @click.outside="open = false"
        class="absolute right-0 mt-2 w-36 bg-white rounded-xl shadow-lg border border-gray-200 z-50">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button wire:click="switchLanguage('<?php echo e($code); ?>')"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-sky-50 hover:text-sky-600 transition-colors <?php echo e($loop->first ? 'rounded-t-xl' : ''); ?> <?php echo e($loop->last ? 'rounded-b-xl' : ''); ?>">
                <span class="mr-2 text-2xl"><?php echo e($data['flag']); ?></span>
                <span><?php echo e(__($data['label'])); ?></span>
            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\IIB\resources\views/livewire/language-switcher.blade.php ENDPATH**/ ?>